{"name": "@circles-zone/dialog-workflow-remote", "version_comment": "https://github.com/circles-zone/dialog-workflow-remote-restapi-typescript-package/pkgs/npm/dialog-workflow-remote", "version": "0.0.32", "description": "dialog workflow remote restapi typescript package", "type": "module", "main": "index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {"import": "./dist/index.js", "require": "./index.cjs"}, "scripts": {"prepare_comment": "TODO The run all and npm-s workaround is so we can run the scripts without using && which only works on Linux. TODO Need to run locally `npm install npm-run-all --save-dev` ", "prepare": "cd .. && husky dialog-workflow-remote-restapi-typescript-package/.husky", "upgrade_packages": "npm update --save", "prebuild_comment": "TODO Should it be `prebuild` or `preinstall` https://stackoverflow.com/questions/41123631/how-to-get-the-version-from-the-package-json-in-typescript", "tsc": "tsc", "compile": "npx tsc", "build_comment": "We recommend doing build and not only compile. To install husky you need one time to do `npm i husky -D`", "build": "node build.mjs", "lint_comment": "TODO We must have eslint.config.mjs instead of .eslintrc.cjs file in ESLint V9", "lint": "eslint **/src/*.ts **/tests/*.ts --report-unused-disable-directives --max-warnings 0", "test": "jest --config ./jest.config.ts --forceExit --detectOpenHandles --coverage", "dev_comment": "TODO Please rename MarketplaceGoodsBackend to your directory", "dev": "nodemon --watch 'MarketplaceGoodsBackend/Src/*.ts --exec ts-node src/index.ts"}, "keywords": [], "author": "", "license": "ISC", "files": ["/dist", "/src"], "dependencies": {"@circles-zone/logger-remote": ">=0.0.164-2639", "@circles-zone/typescript-sdk-remote": ">=0.0.51", "@circles-zone/url-remote": ">=1.1.227", "@circles-zone/user-context-remote": ">=0.0.94-1235", "@jest/globals": ">=29.7.0", "http-status-codes": ">=2.3.0", "node-fetch": "^2.7.0", "ts-node": ">=10.9.2"}, "devDependencies": {"@types/jest": ">=29.5.11", "@types/node": "^20.14.2", "@types/node-fetch": ">=2.6.4", "@typescript-eslint/eslint-plugin": ">=6.13.2", "@typescript-eslint/parser": ">=6.13.2", "dot-notation": ">=1.0.0", "eslint": ">=8.56.0", "eslint-plugin-react": ">=7.33.2", "husky": "^9.0.11", "jest": ">=29.7.0", "jest-mock-fetch": ">=2.0.5", "ts-jest": ">=29.1.1", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1"}}