// This is a compatibility wrapper for the UserContext class
// It provides a CommonJS-compatible way to import the UserContext

// We'll use a local mock implementation instead of the problematic package
export class UserContextTemp {
  private static instance: UserContextTemp | null = null;
  
  private constructor() {}
  
  static getInstance(): UserContextTemp {
    if (!UserContextTemp.instance) {
      UserContextTemp.instance = new UserContextTemp();
    }
    return UserContextTemp.instance;
  }
  
  getUserJwt(): string {
    return "mock-jwt-token";
  }
  
  // static async init(brandName: string, environmentName: string, _credentials: any): Promise<UserContextTemp> {
  static async init(brandName: string, environmentName: string): Promise<UserContextTemp> {
    console.log("UserContext.init() called with:", { brandName, environmentName });
    UserContextTemp.instance = new UserContextTemp();
    return UserContextTemp.instance;
  }
  
  static reset(): void {
    UserContextTemp.instance = null;
  }
}