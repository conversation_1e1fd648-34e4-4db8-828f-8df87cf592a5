import { loggerRemote, RemoteLoggerService } from "@circles-zone/logger-remote";
import { createAuthorizationHttpHeaders ,createHttpBody, LOGGER_IN_MESSAGE_SEPARATOR, OurAxios } from "@circles-zone/typescript-sdk-remote";
import {
  ActionName,
  ComponentName,
  EntityName,
  OurUrl,
} from "@circles-zone/url-remote";
import { UserContextTemp } from "./user-context-wrapper.js";
import fetch from "node-fetch";
import CompoundMessage from "./compoundMessage.js";
import { IncomingMessage as IncomingMessage } from "./interfaces.js";
import { DIALOG_WORKFLOW_REMOTE_RESTAPI_LOGGER_CODE_OBJ } from "./constants.js";
//import { AxiosError } from "axios";
//import axios, { AxiosError } from 'axios';


export class DialogWorkflowRemote {
  private logger: RemoteLoggerService;
  private brandName: string;
  // TODO environmentName: Environment or environmentNameString : string
  private environmentName: string;

  constructor(brandName: string, environmentName: string) {
    this.brandName =brandName
    this.environmentName=environmentName
    this.logger = loggerRemote(brandName, environmentName);
    this.logger.init(
      "init logger",
      DIALOG_WORKFLOW_REMOTE_RESTAPI_LOGGER_CODE_OBJ
    );
  }

  private getApiVersion(environmentName: string): number {
    // Safe access to version dictionary with fallback
    if (environmentName === 'play1') return 1;
    if (environmentName === 'dvlp1') return 1;
    return 1; // Default fallback
  }

  async sendMessage(
    outgoingMessage: string,
    isContinueConverstion: boolean = false
  ) {
    this.logger.start("send_message", {
      brandName: this.brandName,
      environmentName: this.environmentName,
      incomingMessage: outgoingMessage,
    });
    const userContext = UserContextTemp.getInstance();
   
    const header = createAuthorizationHttpHeaders(
      userContext.getUserJwt() as string
    );
    // TODO dialogWorkflowSendMessageEndpointUrl
    const dialogWorkflowSendMessageUrl = OurUrl.endpointUrlV3(
      this.brandName,
      this.environmentName,
      ComponentName.DIALOG_WORKFLOW, 
      EntityName.DIALOG_WORKFLOW, 
      this.getApiVersion(this.environmentName),
      ActionName.SEND_MESSAGE
    );
    if (isContinueConverstion)
      console.warn("dialogWorkflowRemote.ts TODO We need to implement isContinueConverstion in the backend")
    const body = {
      incoming_message: outgoingMessage,
      // isContinueConversation
    };
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let newMessage: any;
    try {
      this.logger.info("dialogWorkflowRemote.ts sending message to dialog workflow", { url: dialogWorkflowSendMessageUrl, header, body });
      // TODO Replace the call to fetch with ourFetch
      const dialogWorkflowSendMessageResponse = await fetch(dialogWorkflowSendMessageUrl, {
        headers: header,
        // TODO https://github.com/jrylan/http-method-enum
        method: "POST",
        body: createHttpBody(body),
      }); 
      this.logger.info("res", dialogWorkflowSendMessageResponse);
      const messageResponse = await dialogWorkflowSendMessageResponse.json();
      // TODO parsedResponse
      const parsedresponse: IncomingMessage =messageResponse;
      this.logger.info("message", messageResponse);
      const compoundMessage = new CompoundMessage(
        parsedresponse,
        this.logger
      );
      newMessage = compoundMessage.getMessage();
      this.logger.info("newMessage", newMessage);

      if (!dialogWorkflowSendMessageResponse.ok) {
        console.error("dialogWorkflowRemote.ts sendMessage() We got from dialog workflow not OK ");
        throw new Error(messageResponse.toString());
      }
      this.logger.info("new message", messageResponse);
      this.logger.info("message", { messageResponse });
      this.logger.end("send_message", { messageResponse });
    } catch (error) {
      this.logger.exception("Error in DialogWorkflowRemote.sendMessage() - Please look on previous log message (i.e. to check if the URL works)", {
        errorInsendMessage: error,
      });
      if (typeof error === "string") {
        throw new Error(error); // Throw the error message as string
      } else {
        // TODO Remove it
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const internalMessage = await OurAxios.axiosErrorToStringAsync(error as any);
        console.error("dialogWorkflowRemote.ts this.sendMessage() "
          +LOGGER_IN_MESSAGE_SEPARATOR+ internalMessage
        );
        // throw new Error(JSON.stringify(error)); // Convert the error object to JSON string and throw
        throw new Error(internalMessage);
      }
    }
    return newMessage;
  }
}