-- create_person_profile_and_subscription_to_existing_user.sql

-- Create a new Person, User, Profile or fix existing users who can't login

-- TODO run this on a list o users we want to create

SET @first_name = "<PERSON>ita<PERSON>";
SET @last_name = "<PERSON>raurig";
SET @username = "eitan.t" COLLATE utf8mb4_0900_ai_ci;
SET @password = 'Eita1Tra%';
SET @gender_id = 2; -- 1 Female 2 Male
SET @birthdate_original = '2003-06-13'; -- TODO birthday -> birthdate in the person_table database
SET @location1 = '955 Phelps Rd, Teaneck';
SET @location2 = "";
-- SET main_full_number_normalized = "";
SET @phone_number_original1 = '************';
SET @phone_number_original2 = '';
-- User's
SET @main_email_address = CONCAT(@username, '@circ.zone'); -- COLLATE utf8mb4_0900_ai_ci;-- '<EMAIL>'; -- '<EMAIL>';
SELECT @main_email_address;
-- Profile 1 Emails
SET @email_address1_1 = @main_email_address; -- profile1
-- SET @email_address1_2 = "<EMAIL>"; -- profile1
-- Profile 1 Groups
-- What are your interest? Associations? Memberships? Music? Technilogy?
SET @group_title1_1 = "Engineering";
SET @group_title1_2 = "Technology";
-- Profile 2 Emails
SET @email_address2_1 = ""; -- profile2 (preferable private email)
SET @email_address2_2 = ""; -- profile2
-- Profile 2 Groups
-- What are your interest? Associations? Memberships? Music? Technilogy?
SET @group_title2_1 = "";
SET @group_title2_2 = "";
-- Profile 3 Emails
SET @email_address3_1 = ""; -- profile3 (preferable school email)



-- TODO person.main_email_person -> person.person.email_address
-- TODO user.main_email_address -> user.user.email_address

-- TODO number per table
SET @number = RAND() * (10000 - 5000) + 5000;
SELECT @number;

-- TODO person.main_email_person -> person.person_email_address
INSERT IGNORE INTO `person`.`person_table` (`number`, identifier, name, `person.main_email_address`
  , first_name, is_first_name_approved
  , last_name_original, last_name, is_last_name_approved, is_last_name_sure, is_last_name_auto_generated
  , last_coordinate
  , gender_id
  , birthday_original
  , is_identity_confirmed, is_approved
  , is_system_data )
VALUES (@number, @number, CONCAT(@first_name, @last_name, @main_email_address), @main_email_address
  , @first_name, TRUE
  , @last_name, @last_name, TRUE, TRUE, TRUE
  , POINT(0, 0)
  , @gender_id
  , @birthdate_original
  , TRUE, TRUE
  , FALSE);
SET @person_id = LAST_INSERT_ID();
SELECT @person_id;
-- Gil's person_id=50050511


INSERT
 IGNORE -- So we can comment this line
 user.user_table (number, `user.main_email_address`, first_name, last_name, username, password, is_approved, name_approved, is_test_data)
VALUES (@number, @main_email_address COLLATE utf8mb4_0900_ai_ci, @first_name, @last_name, @username, @password, 1, 1, 0);
SET @user_id = LAST_INSERT_ID();
SELECT @user_id;

-- Gil's user_id
-- In case the INSERT failed (i.e. Duplicate)
-- TODO Error "Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_unicode_ci,IMPLICIT) for operation '='" in VS Code
SELECT user_id
INTO @user_id
FROM user.user_table
WHERE `user.main_email_address` = @main_email_address COLLATE utf8mb4_0900_ai_ci;
-- WHERE main_email_address COLLATE utf8mb4_0900_ai_ci = @main_email_address COLLATE utf8mb4_0900_ai_ci;
SELECT @user_id;
-- SET @user_id = 5000328; -- Gil's

INSERT
  IGNORE
  INTO profile.profile_table (`number`, `profile.name`, `profile.main_user_id`, `main_person_id`, `profile.main_email_address`
,is_approved, is_test_data)
VALUES (@number, @email_address1_1, @user_id, @person_id, @email_address1_1
, TRUE, FALSE);
-- TODO change to profile_id1
SET @profile_id1 = LAST_INSERT_ID();
SELECT @profile_id1;

-- In case of duplicae in previous step
SELECT profile_id
INTO @profile_id1
FROM profile.profile_table
-- WHERE `profile.main_user_id` = @user_id
WHERE `profile.main_email_address` = @email_address2_1 COLLATE utf8mb4_0900_ai_ci -- utf8mb4_unicode_ci
ORDER BY created_timestamp DESC
LIMIT 1;
SELECT @profile_id1;
-- profile_id = 50000634


-- New
UPDATE person.person_table
SET `person.main_profile_id` = @profile_id1
WHERE person_id=@person_id;


INSERT 
-- IGNORE
INTO `profile_user`.`profile_user_table` (`profile_id`, `user_id`, `is_main`)
VALUES (@profile_id1, @user_id, TRUE);
SET @profile_user_id = LAST_INSERT_ID(); -- TODO Should we change it to main_user_id
SELECT @profile_user_id;

-- What is the purpose? reporting
SELECT profile_user_id
FROM profile_user.profile_user_table
WHERE profile_id = @profile_id1
  AND user_id = @user_id;
-- SELECT * FROM profile_user.profile_user_table WHERE profile_user_id = @profile_user_id;

-- Profile 2
SET @profile2_number = RAND() * (10000 - 5000) + 5000;

INSERT
-- IGNORE
 INTO profile.profile_table (`number`, `profile.name`, `profile.main_user_id`, `main_person_id`, `profile.main_email_address`
    ,is_main, is_approved, is_test_data)
  VALUES (@profile2_number, @email_address2_1, @user_id, @person_id, @email_address2_1
    ,NULL , TRUE, FALSE);
SET @profile_id2 = LAST_INSERT_ID();
SELECT @profile_id2;

-- SELECT for debug (report)
SELECT profile_id, `profile.main_email_address`, end_timestamp
FROM profile.profile_table
WHERE `profile.main_email_address` = @email_address2_1 COLLATE utf8mb4_0900_ai_ci -- utf8mb4_unicode_ci
  -- AND end_timestamp IS NULL
;
SELECT @profile_id1;
-- profile_id = 50000634

-- In case of duplicae in previous step
SELECT profile_id
INTO @profile_id2
FROM profile.profile_table
WHERE `profile.main_email_address` = @email_address2_1 COLLATE utf8mb4_0900_ai_ci
 AND end_timestamp IS NULL
ORDER BY created_timestamp DESC
LIMIT 1;
SELECT @profile_id2;

-- profile_id = 50000634

INSERT IGNORE INTO `profile_user`.`profile_user_table` (`profile_id`, `user_id`, `is_main`)
VALUES (@profile_id2, @user_id, TRUE);
SET @profile_user_id = LAST_INSERT_ID(); -- TODO Should we change it to main_user_id
SELECT @profile_user_id;

-- Profile 3
SET @profile3_number = RAND() * (10000 - 5000) + 5000;

INSERT
-- IGNORE
 INTO profile.profile_table (`number`, `profile.name`, `profile.main_user_id`, `main_person_id`, `profile.main_email_address`
    ,is_main, is_approved, is_test_data)
  VALUES (@profile3_number, @email_address3_1, @user_id, @person_id, @email_address3_1
    ,NULL , TRUE, FALSE);
SET @profile_id3 = LAST_INSERT_ID();
SELECT @profile_id3;

-- SELECT for debug (report)
SELECT profile_id, `profile.main_user_id`, `profile.main_email_address`, end_timestamp
FROM profile.profile_table
WHERE `profile.main_user_id` = @user_id -- utf8mb4_unicode_ci
  -- AND end_timestamp IS NULL
;

-- SELECT for debug (report)
SELECT profile_id, `profile.main_user_id`, `profile.main_email_address`, end_timestamp
FROM profile.profile_table
WHERE `profile.main_email_address` = @email_address3_1 COLLATE utf8mb4_0900_ai_ci -- utf8mb4_unicode_ci
  -- AND end_timestamp IS NULL
;

-- In case of duplicae in previous step
SELECT profile_id
INTO @profile_id3
FROM profile.profile_table
WHERE `profile.main_email_address` = @email_address3_1 COLLATE utf8mb4_0900_ai_ci
 AND end_timestamp IS NULL
ORDER BY created_timestamp DESC
LIMIT 1;
SELECT @profile_id3;
-- profile_id = 50000634

INSERT 
-- IGNORE
INTO `profile_user`.`profile_user_table` (`profile_id`, `user_id`, `is_main`)
VALUES (@profile_id3, @user_id, TRUE);
SET @profile_user_id = LAST_INSERT_ID(); -- TODO Should we change it to main_user_id


-- Per User

INSERT INTO `subscription_user`.`subscription_user_table` (subscription_id, user_id)
VALUES (5, @user_id);

INSERT IGNORE INTO user_pii.user_pii_table (user_id, password_clear_text, password_encrypted)
VALUES (@user_id, @password, @password);

-- TODO we should update the person.main_profile_id

INSERT IGNORE INTO location.location_table (`name`, `address_local_language`,`address_english`, coordinate, is_test_data )
VALUES (@location1, @location1, @location1
, POINT(0, 0)
, FALSE);
SET @location_id1 = LAST_INSERT_ID();

INSERT IGNORE INTO location_profile.location_profile_table (location_id, profile_id, is_test_data )
VALUES (@location_id1, @profile_id1, FALSE);

INSERT IGNORE INTO location.location_table (`name`, `address_local_language`,`address_english`, coordinate, is_test_data )
VALUES (@location2, @location2, @location1
, POINT(0, 0)
, FALSE);
SET @location_id2 = LAST_INSERT_ID();

INSERT IGNORE INTO location_profile.location_profile_table (location_id, profile_id, is_test_data )
VALUES (@location_id2, @profile_id1, FALSE);

INSERT IGNORE INTO phone.phone_table (`number_original`, is_test_data )
VALUES (@phone_number_original1, FALSE);
SET @phone_id1 = LAST_INSERT_ID();

INSERT IGNORE INTO phone_profile.phone_profile_table (phone_id, profile_id, is_test_data )
VALUES (@phone_id1, @profile_id1, FALSE);

INSERT IGNORE INTO phone.phone_table (`number_original`, is_test_data )
VALUES (@phone_number_original2, FALSE);
SET @phone_id2 = LAST_INSERT_ID();

INSERT IGNORE INTO phone_profile.phone_profile_table (phone_id, profile_id, is_test_data )
VALUES (@phone_id2, @profile_id1, FALSE);

INSERT INTO `email_address`.`email_address_table` (`name`, `email_address`
, `email_address_approved`, `is_verified`, `is_system_data`, `is_shared_email_address`, `visibility_id`, `is_test_data`)
VALUES (@email_address1_1, @email_address1_1
, TRUE, TRUE, FALSE, FALSE, 1, FALSE);
SET @email_address_id1_1 = LAST_INSERT_ID();

SELECT email_address_id
INTO @email_address_id1_1
FROM email_address.email_address_table
WHERE email_address=@email_address1_1 AND end_timestamp IS NULL
LIMIT 1 -- TODO
;

INSERT INTO `email_address`.`email_address_table` (`name`, `email_address`
, `email_address_approved`, `is_verified`, `is_system_data`, `is_shared_email_address`, `visibility_id`, `is_test_data`)
VALUES (@email_address1_2, @email_address1_1
, TRUE, TRUE, FALSE, FALSE, 1, FALSE);
SET @email_address_id1_2 = LAST_INSERT_ID();

INSERT INTO `email_address`.`email_address_table` (`name`, `email_address`
, `email_address_approved`, `is_verified`, `is_system_data`, `is_shared_email_address`, `visibility_id`, `is_test_data`)
VALUES (@email_address2_1, @email_address1_1
, TRUE, TRUE, FALSE, FALSE, 1, FALSE);
SET @email_address_id2_1 = LAST_INSERT_ID();

INSERT INTO `email_address`.`email_address_table` (`name`, `email_address`
, `email_address_approved`, `is_verified`, `is_system_data`, `is_shared_email_address`, `visibility_id`, `is_test_data`)
VALUES (@email_address2_2, @email_address1_1
, TRUE, TRUE, FALSE, FALSE, 1, FALSE);
SET @email_address_id2_2 = LAST_INSERT_ID();


-- TODO Add @group_title1_1


SELECT * FROM user.user_general_circles_view WHERE username = @username;