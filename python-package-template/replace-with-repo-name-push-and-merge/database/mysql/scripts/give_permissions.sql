-- TODO If you need permissions to run thos package, please update the file /database/mysql/scripts/give_permissions.sql in your repo
-- Please ask your Team Lead to run it for you in play1, dvlp1, prod1 ...
-- Example:
-- https://github.com/circles-zone/python-package-template/blob/dev/replace-with-repo-name-push-and-merge/database/mysql/scripts/give_permissions.sql

-- SET @username = 'zvi.n';
-- SELECT @username;
USE contact_note;
-- SET @a=CONCAT("'",@username,"@\'%\'\'");
-- SELECT @a;
SET @user_role = '''zvi.n''@''%''';
SET @query = CONCAT("GRANT INSERT, SELECT, UPDATE ON contact_note.* TO ", @user_role);
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
