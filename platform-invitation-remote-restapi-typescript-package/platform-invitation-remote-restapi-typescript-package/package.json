{"name": "@circles-zone/platform-invitation-remote", "version_comment": "https://github.com/circles-zone/platform-invitation-remote-restapi-typescript-package/pkgs/npm/platform-invitation-remote", "version": "0.0.9", "description": "platform invitation Remote TypeScript Package", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "files": ["/dist", "/src"], "scripts": {"prepare_comment": "The run all and npm-s workaround is so we can run the scripts without using && which only works on Linux", "prepare_old": "run-s tsc huskyInstall", "huskyInstall_old": "husky install", "preinstall": "node fix-dependencies.js", "upgrade_packages": "npm update --save", "tsc": "tsc", "compile": "npx tsc", "prebuild_comment": "TODO Should it be `prebuild` or `preinstall` https://stackoverflow.com/questions/41123631/how-to-get-the-version-from-the-package-json-in-typescript", "prebuild": "node -p \"'export const PACKAGE_VERSION = ' + JSON.stringify(require('./package.json').version) + ';'\" > platform-invitation-remote/src/version.ts", "build_comment": "We recommend doing build and not only compile", "build": "node fix-dependencies.js && npm i && npx tsc", "lint_comment": "TODO We must have .eslintrc.cjs file", "lint": "eslint **/src/*.ts **/tests/*.ts --report-unused-disable-directives --max-warnings 0", "test": "NODE_OPTIONS=--experimental-vm-modules jest --config ./jest.config.cjs --forceExit --detectOpenHandles --coverage", "dev_comment": "TODO Please rename MarketplaceGoodsBackend to your directory", "dev": "nodemon --watch 'MarketplaceGoodsBackend/Src/*.ts --exec ts-node src/index.ts", "fix-deps": "node fix-dependencies.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies_comment": "TODO We use user-context-remote only in Test, can we move it to devDependencies", "dependencies": {"@circles-zone/authentication-remote": ">=0.0.84-2593", "@circles-zone/logger-remote": ">=0.0.166-2639 ", "@circles-zone/relationship-type-typeorm-local": ">=0.0.2", "@circles-zone/typescript-sdk-local": ">=0.0.45", "@circles-zone/typescript-sdk-remote": ">=0.0.108", "@circles-zone/url-local": "npm:@circles-zone/url-remote@>=1.1.231", "@circles-zone/url-remote": ">=1.1.231", "@circles-zone/user-context-remote": "^0.0.93-1235", "axios-mock-adapter": "^1.21.5", "node-fetch": "^2.7.0", "ts-node": "^10.9.1"}, "devDependencies_comment": "npm install -D", "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^20.11.17", "@types/node-fetch": "^2.6.4", "@typescript-eslint/eslint-plugin": "^8.24.1", "@typescript-eslint/parser": "^8.24.1", "eslint": "^9.20.1", "eslint-plugin-react-refresh": "^0.4.3", "husky": "^9.1.1", "jest": "^29.7.0", "jest-mock-fetch": "^2.0.5", "npm-run-all": "^4.1.5", "ts-jest": "^29.4.0", "typescript": "^5.7.3", "typescript-eslint": "^8.24.1"}, "resolutions": {"@circles-zone/url-local": "@circles-zone/url-remote"}}