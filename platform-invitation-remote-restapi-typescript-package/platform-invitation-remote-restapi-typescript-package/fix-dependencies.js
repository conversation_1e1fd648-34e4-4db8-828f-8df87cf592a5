#!/usr/bin/env node

// Temp file created by Amazon Q 
// 2DO Shall we delete this file

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create url-local directory if it doesn't exist
const urlLocalDir = path.join(__dirname, 'node_modules', '@circles-zone', 'url-local');
if (!fs.existsSync(urlLocalDir)) {
  fs.mkdirSync(urlLocalDir, { recursive: true });
}

// Create package.json for url-local
const packageJson = {
  name: '@circles-zone/url-local',
  version: '1.1.129',
  description: 'Alias package that redirects to url-remote',
  main: 'index.js',
  types: 'index.d.ts',
  files: ['index.js', 'index.d.ts'],
  dependencies: {
    '@circles-zone/url-remote': '>=1.1.231'
  }
};

fs.writeFileSync(
  path.join(urlLocalDir, 'package.json'),
  JSON.stringify(packageJson, null, 2)
);

// Create index.js for url-local
fs.writeFileSync(
  path.join(urlLocalDir, 'index.js'),
  `// Re-export everything from url-remote
import * as urlRemote from '@circles-zone/url-remote';
export default urlRemote;
export * from '@circles-zone/url-remote';
`
);

// Create index.d.ts for url-local
fs.writeFileSync(
  path.join(urlLocalDir, 'index.d.ts'),
  `// Re-export all types from url-remote
export * from '@circles-zone/url-remote';
`
);

console.log('Successfully created url-local package that redirects to url-remote');