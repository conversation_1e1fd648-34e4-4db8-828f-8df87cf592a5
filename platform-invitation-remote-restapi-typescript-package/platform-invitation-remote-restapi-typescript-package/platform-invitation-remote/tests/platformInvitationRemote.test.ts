import {
    PLATFORM_INVITATION_REMOTE_TYPESCRIPT_COMPONENT_NAME,
    PLATFORM_INVITATION_REMOTE_TYPESCRIPT_COMPONENT_ID,
    PlatformInvitationRemote
} from "../src/platformInvitationRemote";
import {
    ComponentCategory,
    TestingFramework,
    loggerRemote,
} from "@circles-zone/logger-remote";
import { beforeAll, describe, expect, it } from '@jest/globals';
import { UserContext, Tenant } from '@circles-zone/user-context-remote';
import {SECOND_TO_MILLISECOND, populateSystemContext, SystemContext} from '@circles-zone/typescript-sdk-remote'
import * as dotenv from "dotenv";
dotenv.config();

describe("Test platform invitation remote", () => {
    const systemContext: SystemContext = populateSystemContext({})
    const environmentName: string = systemContext.environmentName as string;
    const brandName: string  = systemContext.brandName as string;
    const productUserIdentifier: string  = systemContext.userIdentifier as string;
    const productPassword: string = systemContext.password as string;
    //TODO USE ProfileLocal.getTestFromProfileId()
    const TEST_FROM_PROFILE_ID: string = "1";
    const TEST_TO_FIRST_NAME: string = "Maxim";
    const TEST_TO_LAST_NAME: string = "Spektor";
    const TEST_TO_ORGANIZATION: string = "Circlez";
    const TEST_EMAIL_ADDRESS: string = '<EMAIL>';
    const TEST_PHONE_NUMBER: string = '************';
    const TO_TEST_EVENT_ID: number = 1;

    let platformInvitationRemote: PlatformInvitationRemote;
    const logger = loggerRemote(brandName as string, environmentName as string);
    const logger_init_object = {
        component_id: PLATFORM_INVITATION_REMOTE_TYPESCRIPT_COMPONENT_ID,
        component_name: PLATFORM_INVITATION_REMOTE_TYPESCRIPT_COMPONENT_NAME,
        component_category: ComponentCategory.Unit_Test,
        testing_framework: TestingFramework.Vitest,
        developer_email: "<EMAIL>",
    };
    
    beforeAll(async() => {
        await logger.init("TEST: platform invitation remote-typescript-package", logger_init_object);
        platformInvitationRemote = new PlatformInvitationRemote(brandName as string, environmentName as string);
    });

    it("should create an instance of the platform invitation class", async() => {
        await logger.start("should create an instance of the platform invitation class", {
            platformInvitationRemote: platformInvitationRemote,
        });
        expect(platformInvitationRemote).toBeInstanceOf(PlatformInvitationRemote);
        await logger.end("should create an instance of the platform invitation class");
    }); 
    it("should create a platform invitation ", async () => {
        await logger.start("should create a platform invitation");
        const userContext = await UserContext.init(
            { brandName, 
            environmentName} as Tenant,
            { credentialsData:
            {
                userIdentifier: productUserIdentifier,
                password: productPassword
            }
        }
        );
        
        const userJwt = userContext.getUserJwt() as string
        const createdPlatformInvitation = await platformInvitationRemote.createPlatformInvitation(
            userJwt,
            TEST_FROM_PROFILE_ID,
            TEST_TO_FIRST_NAME,  // Optional
            TEST_TO_LAST_NAME, // Optional
            TEST_TO_ORGANIZATION, // Optional
            TEST_EMAIL_ADDRESS,
            TEST_PHONE_NUMBER,
            TO_TEST_EVENT_ID
        );
            console.log('console log createdPlatformInvitation',createdPlatformInvitation)
        await logger.info("created a platform invitation remote", { createdPlatformInvitation: createdPlatformInvitation });
        expect(createdPlatformInvitation).toBeTruthy();
      expect(() => {}).not.toThrow();
        // expect(typeof createdPlatformInvitation).toBe('string');
        await logger.end("successfully created a platform invitation ");
    } ,30 * SECOND_TO_MILLISECOND); 
})