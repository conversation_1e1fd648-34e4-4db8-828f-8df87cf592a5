import re

from email_address_local.email_address import Email<PERSON>dd<PERSON><PERSON>ocal
from language_remote.lang_code import <PERSON><PERSON><PERSON>
from python_sdk_remote.utilities import our_get_env
from user_local.user_local import UsersLocal
from profile_local.profiles_local import ProfilesLocal


# from profile_local.profiles_local import ProfilesLocal
from src.GetTestIds import GetTestIds
from src.MessageLocal import MessageLocal
from src.Recipient import Recipient

# TODO: improve the tests
MESSAGE_TEST_SENDER_PROFILE_ID = our_get_env("MESSAGE_TEST_SENDER_PROFILE_ID", raise_if_not_found=False)
MESSAGE_TEST_SENDER_PROFILE_ID = int(MESSAGE_TEST_SENDER_PROFILE_ID) if MESSAGE_TEST_SENDER_PROFILE_ID else None

# TODO: use test ids after linking them to all tables in joins
#   As otherwise this will not work on another environment/empty database.
TEST_PROFILE_ID_1 = 50003306  # TODO: ProfilesLocal().get_test_profile_id()
TEST_FIRST_NAME_1 = "May"
TEST_CAMPAIGN_ID_1 = 3  # TODO: get_test_campaign_id()

TEST_PROFILE_ID_2 = 50000458  # TODO: ProfilesLocal().get_test_profile_id()
TEST_FIRST_NAME_2 = "Idan"
TEST_CAMPAIGN_ID_2 = 205  # TODO: get_test_campaign_id()

TEST_PROFILE_ID_3 = 2  # TODO: ProfilesLocal().get_test_profile_id()
# TODO Let's have multiple tests of different templates (form, non-form ...)
TEST_MESSAGE_TEMPLATE_ID = 9010  # 9001  # TODO: get_test_message_template_id()
TEST_EMAIL_ADDRESS = EmailAddressesLocal.get_test_email_address()

TEST_FORM_ID = 5  # TODO: get_test_form_id()

TEST_TELEPHONE_NUMBER = "0501234567"
TEST_BODY = "Hello ${{ to.first_name }}, how are you ${{feeling|doing}}? You otp is ${{otp}}."


def test_recipient_get_user_dict():
    # TODO use system_id.py file generated by Sql2Code

    FACEBOOK_SYSTEM_ID = 2
    users_local = UsersLocal(is_test_data=True)
    user_id = users_local.get_test_user_id()

    recipient = Recipient(user_id=user_id)

    user_dict = recipient.get_user_dict_by_system_id(FACEBOOK_SYSTEM_ID)

    assert user_dict == {'phone.full_number_normalized': [], 'user_external_id': [], 'username': []}, f"Expected: {user_dict}, Actual: {user_dict}"

    profiles_local = ProfilesLocal(is_test_data=True)
    profile_id = profiles_local.get_test_profile_id()

    recipient = Recipient(profile_id=profile_id)

    profile_dict = recipient.get_user_dict_by_system_id(FACEBOOK_SYSTEM_ID)

    assert profile_dict == {'phone.full_number_normalized': [], 'user_external_id': [], 'username': []}, f"Expected: {profile_dict}, Actual: {profile_dict}"  # noqa

    # TODO Please develop/use get_test_person_id() from person-local and not Magic Numbers which is not good programming practice
    recipient = Recipient(person_id=103)

    user_dict = recipient.get_user_dict_by_system_id(FACEBOOK_SYSTEM_ID)
    assert user_dict == {'phone.full_number_normalized': [], 'user_external_id': [], 'username': []}, f"Expected: {user_dict}, Actual: {user_dict}"

    # TODO Please use/create get_test_telephone_number() from phone-local-main... package. Can we create such?
    recipient = Recipient(telephone_number="5166333899")

    user_dict = recipient.get_user_dict_by_system_id(FACEBOOK_SYSTEM_ID)
    assert user_dict == {'phone.full_number_normalized': [], 'user_external_id': [], 'username': []}, f"Expected: {user_dict}, Actual: {user_dict}"

    if hasattr(users_local, "connection") and users_local.connection:
        users_local.connection.close()
    if hasattr(profiles_local, "connection") and profiles_local.connection:
        profiles_local.connection.close()


def test_form():
    # recipient = Recipient(profile_id=TEST_PROFILE_ID_1,
    #                       email_address_str=EmailAddressesLocal.get_test_email_address(),
    #                       preferred_lang_code_str=LangCode.ENGLISH.value,
    #                       telephone_number=TEST_TELEPHONE_NUMBER)

    message_local = MessageLocal(form_id=TEST_FORM_ID, is_debug=True, is_test_data=True,
                                 sender_profile_id=MESSAGE_TEST_SENDER_PROFILE_ID)
    assert message_local.get_compound_message_dict()

    # TODO Why those are mandatory?
    if hasattr(message_local, "connection") and message_local.connection:
        message_local.connection.close()


# TODO Preferabe instead of _1 provide meaningful test scenario
# def test_campaign_1():  # TODO: uncomment
#     """test_instance"""
#     recipient_1 = Recipient(profile_id=TEST_PROFILE_ID_1, first_name=TEST_FIRST_NAME_1,
#                             email_address_str=EmailAddressesLocal.get_test_email_address(),
#                             preferred_lang_code_str=LangCode.ENGLISH.value,
#                             telephone_number=TEST_TELEPHONE_NUMBER)
#     recipient_2 = Recipient(profile_id=TEST_PROFILE_ID_2, first_name=TEST_FIRST_NAME_2,
#                             email_address_str=EmailAddressesLocal.get_test_email_address(),
#                             preferred_lang_code_str=LangCode.ENGLISH.value,
#                             telephone_number=TEST_TELEPHONE_NUMBER)
#     message_local = MessageLocal(recipients=[recipient_1, recipient_2],
#                                  campaign_id=TEST_CAMPAIGN_ID_1,
#                                  is_test_data=True)
#     assert message_local
#     assert message_local.get_compound_message_dict() != {}
#     assert message_local.get_compound_message_dict(MessageChannel.SMS) != {}
#     assert message_local.get_body_text_after_template_processing(recipient_2) != ""


def test_campaign_2():
    """test_instance"""
    # recipient = Recipient(profile_id=TEST_PROFILE_ID_3,
    #                       email_address_str=EmailAddressesLocal.get_test_email_address(),
    #                       preferred_lang_code_str=LangCode.ENGLISH.value,
    #                       telephone_number=TEST_TELEPHONE_NUMBER)
    message_local = MessageLocal(campaign_id=TEST_CAMPAIGN_ID_2,  # recipients=[recipient],
                                 is_test_data=True, sender_profile_id=MESSAGE_TEST_SENDER_PROFILE_ID)
    assert message_local.get_compound_message_dict()

    if hasattr(message_local, "connection") and message_local.connection:
        message_local.connection.close()


def test_template():
    """test_template"""
    recipient = Recipient(profile_id=TEST_PROFILE_ID_3,
                          email_address_str=EmailAddressesLocal.get_test_email_address(),
                          preferred_lang_code_str=LangCode.ENGLISH.value,
                          telephone_number=TEST_TELEPHONE_NUMBER)
    message_local = MessageLocal(message_template_id=TEST_MESSAGE_TEMPLATE_ID, recipients=[recipient],
                                 is_test_data=True, sender_profile_id=MESSAGE_TEST_SENDER_PROFILE_ID)
    assert message_local.get_compound_message_dict()

    if hasattr(message_local, "connection") and message_local.connection:
        message_local.connection.close()


def test_recipient():
    """test_recipient"""
    recipient = Recipient(profile_id=TEST_PROFILE_ID_1,
                          email_address_str=TEST_EMAIL_ADDRESS,
                          preferred_lang_code_str=LangCode.ENGLISH.value)
    recipient_dict = recipient.to_dict()
    assert recipient_dict == {'email_address_str': TEST_EMAIL_ADDRESS,
                              'title_per_lang_code_str': {},
                              'preferred_lang_code_str': LangCode.ENGLISH.value,
                              'profile_id': TEST_PROFILE_ID_1}

    # test __repr__
    assert [recipient.to_dict()] == [recipient_dict]

    recipient2 = Recipient.from_dict(recipient_dict)
    assert recipient_dict == recipient2.to_dict()

    assert LangCode.ENGLISH == recipient.get_preferred_lang_code()
    assert LangCode.ENGLISH.value == recipient.get_preferred_lang_code_str()


def test_get_test_message_id():
    test_message_id = GetTestIds().get_test_message_id()
    assert test_message_id > 0

    # test init with message_id
    recipient = Recipient(profile_id=TEST_PROFILE_ID_1, telephone_number=TEST_TELEPHONE_NUMBER)
    message_local = MessageLocal(message_id=test_message_id, recipients=[recipient],
                                 sender_profile_id=MESSAGE_TEST_SENDER_PROFILE_ID)
    assert message_local.get_compound_message_dict()


def test_body():
    recipient = Recipient(profile_id=TEST_PROFILE_ID_1, telephone_number=TEST_TELEPHONE_NUMBER,
                          first_name=TEST_FIRST_NAME_1)

    message_local = MessageLocal(recipients=[recipient], original_body=TEST_BODY,
                                 sender_profile_id=MESSAGE_TEST_SENDER_PROFILE_ID)
    body = message_local.get_body_text_after_template_processing(recipient=recipient)

    expected_regex = rf"Hello {TEST_FIRST_NAME_1}, how are you (feeling|doing)\? You otp is \d+."
    assert re.match(expected_regex, body), f"Expected: {expected_regex}, Actual: {body}"

    if hasattr(message_local, "connection") and message_local.connection:
        message_local.connection.close()


if __name__ == "__main__":
    # test_form()
    # test_campaign_1()
    # test_campaign_2()
    # test_template()
    # test_recipient()
    # test_get_test_message_id()
    # test_body()
    test_recipient_get_user_dict()
