# flake8: noqa
import sys

import pandas as pd

sys.path.append("message-local-python-package/message_local")
from src.CompoundMessageVerification import CompoundMessageVerification  # noqa: E402


def test_first_name_in_english_and_hebrew_does_not_match():
    """
    Test the verify_compound_message function.
    """
    # Existing test case
    comp = {
        "message_id": 4,
        "compound_message_json": '{"data":{"EMAIL":{"Page":[{"page_number":null,"MessageTemplates":[{"message_template_id":3,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":null,"profile_id":2,"is_required":false,"question_id":7,"variable_id":24,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"Tal, What date is your birthday?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":107},{"Profiles":[{"is_visible":null,"profile_id":1,"is_required":false,"question_id":15,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"Tal, Which neighbourhood do you live?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":115}]}]}]}}}',
    }
    comp_heb = {
        "message_id": 4,
        "compound_message_json": '{"data":{"EMAIL":{"Page":[{"page_number":null,"MessageTemplates":[{"message_template_id":3,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":null,"profile_id":1,"is_required":false,"question_id":30001,"variable_id":24,"possible_answers":[],"body_per_language":null,"preferred_language":"he","subject_per_language":{},"question_per_language":{"en": "Tal Are you coming to MuniExpo (Free)? - I\'ll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False","he":"רשי מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":107},{"Profiles":[{"is_visible":null,"profile_id":1,"is_required":false,"question_id":15,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"Tal, Which neighbourhood do you live?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":115}]}]}]}}}',
    }
    compound = CompoundMessageVerification()
    # print(compound.check_for_errors(pd.DataFrame([comp, comp_heb])))
    assert compound.check_for_errors(pd.DataFrame([comp, comp_heb])) == [
        "Question_per_lang_errors: Message_id: 4, Channel: EMAIL, Profile_id: 2, Question_id: 7, First name of user: Ido Hebrew name in profile: טל, Question in database: en: ${{ to.first_name }}, What date is your birthday?, Question in Json: en: Tal, What date is your birthday?, ERRORS: EN: First name in question doesn't match user name of that language",
        "Question_per_lang_errors: Message_id: 4, Channel: EMAIL, Profile_id: 1, Question_id: 30001, First name of user: Tal Hebrew name in profile: ראשי, Question in database: en: ${{ to.first_name }} Are you coming to MuniExpo (Free)? - I'll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in database: he: ${{ to.first_name }} מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in Json: en: Tal Are you coming to MuniExpo (Free)? - I'll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in Json: he: רשי מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, ERRORS: HE: First name in question doesn't match user name of that language",
    ]


def test_profile_or_user_does_not_exist():
    """
    Until user_ml_table is established, will always return no hebrew version of name
    """
    # Existing test case
    comp = {
        "message_id": 4,
        "compound_message_json": '{"data":{"EMAIL":{"Page":[{"page_number":null,"MessageTemplates":[{"message_template_id":3,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":null,"profile_id":50003425,"is_required":false,"question_id":30001,"variable_id":24,"possible_answers":[],"body_per_language":null,"preferred_language":"he","subject_per_language":{},"question_per_language":{"en": "Tal Are you coming to MuniExpo (Free)? - I\'ll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False","he":"רשי מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":107},{"Profiles":[{"is_visible":null,"profile_id":1,"is_required":false,"question_id":15,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"Tal, Which neighbourhood do you live?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":115}]}]}]}}}',
    }
    compound = CompoundMessageVerification()
    assert compound.check_for_errors(comp) == [
        "Question_per_lang_errors: Message_id: 4, Channel: EMAIL, Profile_id: 50003425, Question_id: 30001, First name of user: PF_Deleted_or_Missing Hebrew name in profile: None, Question in database: en: ${{ to.first_name }} Are you coming to MuniExpo (Free)? - I'll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in database: he: ${{ to.first_name }} מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in Json: en: Tal Are you coming to MuniExpo (Free)? - I'll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in Json: he: רשי מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, ERRORS: User or Profile is missing or deleted"
    ]


def test_question_template_does_not_match():
    """
    Test that templates without a name change match their templates or not
    """
    comp = {
        "message_id": 4222,
        "compound_message_json": '{"data":{"DEFAULT":{"Page":[{"page_number":1,"MessageTemplates":[{"index_name":null,"index_number":[[1,2,3]],"message_seq_in_page":1,"message_template_id":3000,"message_template_name":"First name, last name, birthday and gender","MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":1,"is_required":false,"question_id":601,"variable_id":6,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What is your firt name?","he":"מה שם הפרטי שלך?"},"default_question_possible_answer_id":null}],"body_templates":{},"question_schema":"{ \\"type\\": \\"string\\", \\"title\\": \\"First Name\\" }","question_uischema":"{}","subject_templates":{},"question_templates":{"en":"What is your first name?","he":"מה השם הפרטי שלך?"},"message_template_text_block_id":3001,"message_template_text_block_seq":1,"message_template_text_block_name":"First Name"}]}]}]}}}',
    }
    compound = CompoundMessageVerification()
    assert compound.check_for_errors(comp) == [
        "Question_per_lang_errors: Message_id: 4222, Channel: DEFAULT, Profile_id: 1, Question_id: 601, First name of user: Tal Hebrew name in profile: ראשי, Question in database: en: What is your first name?, Question in database: he: מה השם הפרטי שלך?, Question in Json: en: What is your firt name?, Question in Json: he: מה שם הפרטי שלך?, ERRORS: EN: Question in Json doesn't match the question in the template, HE: Question in Json doesn't match the question in the template"
    ]


def test_name_lang_does_not_match():
    """
    Test that templates without a name change match their templates or not
    """
    comp = {
        "message_id": 4,
        "compound_message_json": '{"data":{"EMAIL":{"Page":[{"page_number":null,"MessageTemplates":[{"message_template_id":3,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":null,"profile_id":1,"is_required":false,"question_id":30001,"variable_id":24,"possible_answers":[],"body_per_language":null,"preferred_language":"he","subject_per_language":{},"question_per_language":{"en": "טל Are you coming to MuniExpo (Free)? - I\'ll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False","he":"Tal מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":107},{"Profiles":[{"is_visible":null,"profile_id":1,"is_required":false,"question_id":15,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"Tal, Which neighbourhood do you live?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":115}]}]}]}}}',
    }
    compound = CompoundMessageVerification()
    assert (compound.check_for_errors(comp)) == [
        "Question_per_lang_errors: Message_id: 4, Channel: EMAIL, Profile_id: 1, Question_id: 30001, First name of user: Tal Hebrew name in profile: ראשי, Question in database: en: ${{ to.first_name }} Are you coming to MuniExpo (Free)? - I'll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in database: he: ${{ to.first_name }} מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in Json: en: טל Are you coming to MuniExpo (Free)? - I'll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in Json: he: Tal מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, ERRORS: EN: First name in question doesn't match user name of that language, EN: Name is not in English in question_per_language, HE: First name in question doesn't match user name of that language, HE: Name is not in Hebrew in question_per_language"
    ]


def test_question_name_in_hebrew_missing():
    comp = {
        "message_id": 4,
        "compound_message_json": '{"data":{"EMAIL":{"Page":[{"page_number":null,"MessageTemplates":[{"message_template_id":3,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":null,"profile_id":50003385,"is_required":false,"question_id":30001,"variable_id":24,"possible_answers":[],"body_per_language":null,"preferred_language":"he","subject_per_language":{},"question_per_language":{"en": "Dima Are you coming to MuniExpo (Free)? - I\'ll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False","he":"רשי מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":107},{"Profiles":[{"is_visible":null,"profile_id":1,"is_required":false,"question_id":15,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"Tal, Which neighbourhood do you live?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":115}]}]}]}}}',
    }
    compound = CompoundMessageVerification()
    assert (compound.check_for_errors(comp)) == [
        "Question_per_lang_errors: Message_id: 4, Channel: EMAIL, Profile_id: 50003385, Question_id: 30001, First name of user: Dima Hebrew name in profile: None, Question in database: en: ${{ to.first_name }} Are you coming to MuniExpo (Free)? - I'll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in database: he: ${{ to.first_name }} מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in Json: en: Dima Are you coming to MuniExpo (Free)? - I'll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in Json: he: רשי מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, ERRORS: HE: First name in question doesn't match user name of that language, HE: No Hebrew Version of name in database"
    ]


def test_body_works():
    comp = {
        "message_id": 9,
        "compound_message_json": '{"data":{"DEFAULT":{"Page":[{"page_number":1,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":2,"is_required":false,"question_id":null,"variable_id":null,"possible_answers":[],"body_per_language":{"en":"Ido Best wishes or the Israeli 76th Independence Day","he":"טל צרור איחולים ליום העצמאות של ישראל"},"preferred_language":"en","subject_per_language":{},"question_per_language":null,"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9010}]}]},{"page_number":2,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":1,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null},{"is_visible":1,"profile_id":2,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9011}]}]}]}},"json_version":"240416"}',
    }
    compound = CompoundMessageVerification()
    results = compound.check_for_errors(comp)
    assert results == []


def test_body_name_lang_does_not_match():
    comp = {
        "message_id": 9,
        "compound_message_json": '{"data":{"DEFAULT":{"Page":[{"page_number":1,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":2,"is_required":false,"question_id":null,"variable_id":null,"possible_answers":[],"body_per_language":{"en":"טל Best wishes or the Israeli 76th Independence Day","he":"Ido צרור איחולים ליום העצמאות של ישראל"},"preferred_language":"en","subject_per_language":{},"question_per_language":null,"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9010}]}]},{"page_number":2,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":1,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null},{"is_visible":1,"profile_id":2,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9011}]}]}]}},"json_version":"240416"}',
    }
    compound = CompoundMessageVerification()
    assert (compound.check_for_errors(comp)) == [
        "Body_per_lang_errors: Message_id: 9, Channel: DEFAULT, Profile_id: 2, First name of user: Ido Hebrew name in profile: טל, Question_id: None, Message_template_text_block_id: 9010, body in table: {'en': '${{ to.first_name }} Best wishes or the Israeli 76th Independence Day', 'he': '${{ to.first_name }} צרור איחולים ליום העצמאות של ישראל'}, Body in en: טל Best wishes or the Israeli 76th Independence Day, Body in he: Ido צרור איחולים ליום העצמאות של ישראל, ERRORS: EN: First name in body_per_lang doesn't match user name of that language, EN: Name is not in English in body_per_language, HE: First name in body_per_lang doesn't match user name of that language, HE: Name is not in Hebrew in body_per_language"
    ]


def test_profile_or_user_does_not_exist_in_body():
    comp = {
        "message_id": 9,
        "compound_message_json": '{"data":{"DEFAULT":{"Page":[{"page_number":1,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":50003425,"is_required":false,"question_id":null,"variable_id":null,"possible_answers":[],"body_per_language":{"en":"Ido Best wishes or the Israeli 76th Independence Day","he":"טל צרור איחולים ליום העצמאות של ישראל"},"preferred_language":"en","subject_per_language":{},"question_per_language":null,"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9010}]}]},{"page_number":2,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":1,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null},{"is_visible":1,"profile_id":2,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9011}]}]}]}},"json_version":"240416"}',
    }
    compound = CompoundMessageVerification()
    assert (compound.check_for_errors(comp)) == [
        "Body_per_lang_errors: Message_id: 9, Channel: DEFAULT, Profile_id: 50003425, First name of user: PF_Deleted_or_Missing Hebrew name in profile: None, Question_id: None, Message_template_text_block_id: 9010, body in table: {'en': '${{ to.first_name }} Best wishes or the Israeli 76th Independence Day', 'he': '${{ to.first_name }} צרור איחולים ליום העצמאות של ישראל'}, Body in en: Ido Best wishes or the Israeli 76th Independence Day, Body in he: טל צרור איחולים ליום העצמאות של ישראל, ERRORS: User or Profile is missing or deleted"
    ]


def test_template_does_not_match_in_body():
    comp = {
        "message_id": 9,
        "compound_message_json": '{"data":{"DEFAULT":{"Page":[{"page_number":1,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":2,"is_required":false,"question_id":null,"variable_id":null,"possible_answers":[],"body_per_language":{"en":"Ido Best wishes o the Israeli 76th Independence Day","he":"טל צור איחולים ליום העצמאות של ישראל"},"preferred_language":"en","subject_per_language":{},"question_per_language":null,"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9010}]}]},{"page_number":2,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":2,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null},{"is_visible":1,"profile_id":2,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9011}]}]}]}},"json_version":"240416"}',
    }
    compound = CompoundMessageVerification()
    assert (compound.check_for_errors(comp)) == [
        "Body_per_lang_errors: Message_id: 9, Channel: DEFAULT, Profile_id: 2, First name of user: Ido Hebrew name in profile: טל, Question_id: None, Message_template_text_block_id: 9010, body in table: {'en': '${{ to.first_name }} Best wishes or the Israeli 76th Independence Day', 'he': '${{ to.first_name }} צרור איחולים ליום העצמאות של ישראל'}, Body in en: Ido Best wishes o the Israeli 76th Independence Day, Body in he: טל צור איחולים ליום העצמאות של ישראל, ERRORS: EN: body_per_lang in Json doesn't match the body in the template, HE: body_per_lang in Json doesn't match the body in the template"
    ]


def test_heb_and_eng_name_does_not_match_body():
    comp = {
        "message_id": 9,
        "compound_message_json": '{"data":{"DEFAULT":{"Page":[{"page_number":1,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":2,"is_required":false,"question_id":null,"variable_id":null,"possible_answers":[],"body_per_language":{"en":"Io Best wishes or the Israeli 76th Independence Day","he":"ט צרור איחולים ליום העצמאות של ישראל"},"preferred_language":"en","subject_per_language":{},"question_per_language":null,"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9010}]}]},{"page_number":2,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":1,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null},{"is_visible":1,"profile_id":2,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9011}]}]}]}},"json_version":"240416"}',
    }
    compound = CompoundMessageVerification()
    assert (compound.check_for_errors(comp)) == [
        "Body_per_lang_errors: Message_id: 9, Channel: DEFAULT, Profile_id: 2, First name of user: Ido Hebrew name in profile: טל, Question_id: None, Message_template_text_block_id: 9010, body in table: {'en': '${{ to.first_name }} Best wishes or the Israeli 76th Independence Day', 'he': '${{ to.first_name }} צרור איחולים ליום העצמאות של ישראל'}, Body in en: Io Best wishes or the Israeli 76th Independence Day, Body in he: ט צרור איחולים ליום העצמאות של ישראל, ERRORS: EN: First name in body_per_lang doesn't match user name of that language, HE: First name in body_per_lang doesn't match user name of that language"
    ]


def test_heb_name_not_exists_in_body():
    comp = {
        "message_id": 9,
        "compound_message_json": '{"data":{"DEFAULT":{"Page":[{"page_number":1,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":50003385,"is_required":false,"question_id":null,"variable_id":null,"possible_answers":[],"body_per_language":{"en":"Dima Best wishes or the Israeli 76th Independence Day","he":"צרור איחולים ליום העצמאות של ישראל"},"preferred_language":"en","subject_per_language":{},"question_per_language":null,"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9010}]}]},{"page_number":2,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":1,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null},{"is_visible":1,"profile_id":2,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9011}]}]}]}},"json_version":"240416"}',
    }
    compound = CompoundMessageVerification()
    assert (compound.check_for_errors(comp)) == [
        "Body_per_lang_errors: Message_id: 9, Channel: DEFAULT, Profile_id: 50003385, First name of user: Dima Hebrew name in profile: None, Question_id: None, Message_template_text_block_id: 9010, body in table: {'en': '${{ to.first_name }} Best wishes or the Israeli 76th Independence Day', 'he': '${{ to.first_name }} צרור איחולים ליום העצמאות של ישראל'}, Body in en: Dima Best wishes or the Israeli 76th Independence Day, Body in he: צרור איחולים ליום העצמאות של ישראל, ERRORS: HE: First name in body_per_lang doesn't match user name of that language, HE: No Hebrew Version of name in database"
    ]


def test_empty_json():
    comp = {
        "message_id": 9,
        "compound_message_json": '{"data": {}, "json_version": "240416"}',
    }
    compound = CompoundMessageVerification()
    assert (compound.check_for_errors(comp)) == ["EMPTY MESSAGE ERROR, MESSAGE_ID: 9"]


def massive_test():
    a = {
        "message_id": 4,
        "compound_message_json": '{"data":{"EMAIL":{"Page":[{"page_number":null,"MessageTemplates":[{"message_template_id":3,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":null,"profile_id":2,"is_required":false,"question_id":7,"variable_id":24,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"Tal, What date is your birthday?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":107},{"Profiles":[{"is_visible":null,"profile_id":1,"is_required":false,"question_id":15,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"Tal, Which neighbourhood do you live?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":115}]}]}]}}}',
    }
    b = {
        "message_id": 5,
        "compound_message_json": '{"data":{"EMAIL":{"Page":[{"page_number":null,"MessageTemplates":[{"message_template_id":3,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":null,"profile_id":1,"is_required":false,"question_id":30001,"variable_id":24,"possible_answers":[],"body_per_language":null,"preferred_language":"he","subject_per_language":{},"question_per_language":{"en": "Tal Are you coming to MuniExpo (Free)? - I\'ll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False","he":"רשי מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":107},{"Profiles":[{"is_visible":null,"profile_id":1,"is_required":false,"question_id":15,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"Tal, Which neighbourhood do you live?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":115}]}]}]}}}',
    }
    c = {
        "message_id": 6,
        "compound_message_json": '{"data":{"EMAIL":{"Page":[{"page_number":null,"MessageTemplates":[{"message_template_id":3,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":null,"profile_id":50003425,"is_required":false,"question_id":30001,"variable_id":24,"possible_answers":[],"body_per_language":null,"preferred_language":"he","subject_per_language":{},"question_per_language":{"en": "Tal Are you coming to MuniExpo (Free)? - I\'ll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False","he":"רשי מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":107},{"Profiles":[{"is_visible":null,"profile_id":1,"is_required":false,"question_id":15,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"Tal, Which neighbourhood do you live?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":115}]}]}]}}}',
    }
    d = {
        "message_id": 7,
        "compound_message_json": '{"data":{"DEFAULT":{"Page":[{"page_number":1,"MessageTemplates":[{"index_name":null,"index_number":[[1,2,3]],"message_seq_in_page":1,"message_template_id":3000,"message_template_name":"First name, last name, birthday and gender","MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":1,"is_required":false,"question_id":601,"variable_id":6,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What is your firt name?","he":"מה שם הפרטי שלך?"},"default_question_possible_answer_id":null}],"body_templates":{},"question_schema":"{ \\"type\\": \\"string\\", \\"title\\": \\"First Name\\" }","question_uischema":"{}","subject_templates":{},"question_templates":{"en":"What is your first name?","he":"מה השם הפרטי שלך?"},"message_template_text_block_id":3001,"message_template_text_block_seq":1,"message_template_text_block_name":"First Name"}]}]}]}}}',
    }
    e = {
        "message_id": 8,
        "compound_message_json": '{"data":{"EMAIL":{"Page":[{"page_number":null,"MessageTemplates":[{"message_template_id":3,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":null,"profile_id":1,"is_required":false,"question_id":30001,"variable_id":24,"possible_answers":[],"body_per_language":null,"preferred_language":"he","subject_per_language":{},"question_per_language":{"en": "טל Are you coming to MuniExpo (Free)? - I\'ll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False","he":"Tal מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":107},{"Profiles":[{"is_visible":null,"profile_id":1,"is_required":false,"question_id":15,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"Tal, Which neighbourhood do you live?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":115}]}]}]}}}',
    }
    f = {
        "message_id": 9,
        "compound_message_json": '{"data":{"EMAIL":{"Page":[{"page_number":null,"MessageTemplates":[{"message_template_id":3,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":null,"profile_id":50003385,"is_required":false,"question_id":30001,"variable_id":24,"possible_answers":[],"body_per_language":null,"preferred_language":"he","subject_per_language":{},"question_per_language":{"en": "Dima Are you coming to MuniExpo (Free)? - I\'ll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False","he":"רשי מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":107},{"Profiles":[{"is_visible":null,"profile_id":1,"is_required":false,"question_id":15,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"Tal, Which neighbourhood do you live?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":115}]}]}]}}}',
    }
    g = {
        "message_id": 11,
        "compound_message_json": '{"data":{"DEFAULT":{"Page":[{"page_number":1,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":2,"is_required":false,"question_id":null,"variable_id":null,"possible_answers":[],"body_per_language":{"en":"טל Best wishes or the Israeli 76th Independence Day","he":"Ido צרור איחולים ליום העצמאות של ישראל"},"preferred_language":"en","subject_per_language":{},"question_per_language":null,"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9010}]}]},{"page_number":2,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":1,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null},{"is_visible":1,"profile_id":2,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9011}]}]}]}},"json_version":"240416"}',
    }
    h = {
        "message_id": 12,
        "compound_message_json": '{"data":{"DEFAULT":{"Page":[{"page_number":1,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":50003425,"is_required":false,"question_id":null,"variable_id":null,"possible_answers":[],"body_per_language":{"en":"Ido Best wishes or the Israeli 76th Independence Day","he":"טל צרור איחולים ליום העצמאות של ישראל"},"preferred_language":"en","subject_per_language":{},"question_per_language":null,"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9010}]}]},{"page_number":2,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":1,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null},{"is_visible":1,"profile_id":2,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9011}]}]}]}},"json_version":"240416"}',
    }
    i = {
        "message_id": 13,
        "compound_message_json": '{"data":{"DEFAULT":{"Page":[{"page_number":1,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":2,"is_required":false,"question_id":null,"variable_id":null,"possible_answers":[],"body_per_language":{"en":"Ido Best wishes o the Israeli 76th Independence Day","he":"טל צור איחולים ליום העצמאות של ישראל"},"preferred_language":"en","subject_per_language":{},"question_per_language":null,"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9010}]}]},{"page_number":2,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":2,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null},{"is_visible":1,"profile_id":2,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9011}]}]}]}},"json_version":"240416"}',
    }
    j = {
        "message_id": 14,
        "compound_message_json": '{"data":{"DEFAULT":{"Page":[{"page_number":1,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":2,"is_required":false,"question_id":null,"variable_id":null,"possible_answers":[],"body_per_language":{"en":"Io Best wishes or the Israeli 76th Independence Day","he":"ט צרור איחולים ליום העצמאות של ישראל"},"preferred_language":"en","subject_per_language":{},"question_per_language":null,"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9010}]}]},{"page_number":2,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":1,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null},{"is_visible":1,"profile_id":2,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9011}]}]}]}},"json_version":"240416"}',
    }
    k = {
        "message_id": 15,
        "compound_message_json": '{"data":{"DEFAULT":{"Page":[{"page_number":1,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":50003385,"is_required":false,"question_id":null,"variable_id":null,"possible_answers":[],"body_per_language":{"en":"Dima Best wishes or the Israeli 76th Independence Day","he":"צרור איחולים ליום העצמאות של ישראל"},"preferred_language":"en","subject_per_language":{},"question_per_language":null,"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9010}]}]},{"page_number":2,"MessageTemplates":[{"message_template_id":9010,"MessageTemplateTextBlocks":[{"Profiles":[{"is_visible":1,"profile_id":1,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null},{"is_visible":1,"profile_id":2,"is_required":false,"question_id":9011,"variable_id":null,"possible_answers":[],"body_per_language":null,"preferred_language":"en","subject_per_language":{},"question_per_language":{"en":"What are you planning to do in the Israeli Independence Day?","he":"מה אתם מתכננים לעשות ביום העצמאות?"},"default_question_possible_answer_id":null}],"question_schema":null,"question_uischema":null,"message_template_text_block_id":9011}]}]}]}},"json_version":"240416"}',
    }
    l = {
        "message_id": 16,
        "compound_message_json": '{"data": {}, "json_version": "240416"}',
    }
    compound = CompoundMessageVerification()
    assert compound.check_for_errors(
        pd.DataFrame([a, b, c, d, e, f, g, h, i, j, k, l])
    ) == [
        "Question_per_lang_errors: Message_id: 4, Channel: EMAIL, Profile_id: 2, Question_id: 7, First name of user: Ido Hebrew name in profile: טל, Question in database: en: ${{ to.first_name }}, What date is your birthday?, Question in Json: en: Tal, What date is your birthday?, ERRORS: EN: First name in question doesn't match user name of that language",
        "Question_per_lang_errors: Message_id: 5, Channel: EMAIL, Profile_id: 1, Question_id: 30001, First name of user: Tal Hebrew name in profile: ראשי, Question in database: en: ${{ to.first_name }} Are you coming to MuniExpo (Free)? - I'll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in database: he: ${{ to.first_name }} מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in Json: en: Tal Are you coming to MuniExpo (Free)? - I'll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in Json: he: רשי מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, ERRORS: HE: First name in question doesn't match user name of that language",
        "Question_per_lang_errors: Message_id: 6, Channel: EMAIL, Profile_id: 50003425, Question_id: 30001, First name of user: PF_Deleted_or_Missing Hebrew name in profile: None, Question in database: en: ${{ to.first_name }} Are you coming to MuniExpo (Free)? - I'll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in database: he: ${{ to.first_name }} מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in Json: en: Tal Are you coming to MuniExpo (Free)? - I'll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in Json: he: רשי מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, ERRORS: User or Profile is missing or deleted",
        "Question_per_lang_errors: Message_id: 7, Channel: DEFAULT, Profile_id: 1, Question_id: 601, First name of user: Tal Hebrew name in profile: ראשי, Question in database: en: What is your first name?, Question in database: he: מה השם הפרטי שלך?, Question in Json: en: What is your firt name?, Question in Json: he: מה שם הפרטי שלך?, ERRORS: EN: Question in Json doesn't match the question in the template, HE: Question in Json doesn't match the question in the template",
        "Question_per_lang_errors: Message_id: 8, Channel: EMAIL, Profile_id: 1, Question_id: 30001, First name of user: Tal Hebrew name in profile: ראשי, Question in database: en: ${{ to.first_name }} Are you coming to MuniExpo (Free)? - I'll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in database: he: ${{ to.first_name }} מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in Json: en: טל Are you coming to MuniExpo (Free)? - I'll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in Json: he: Tal מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, ERRORS: EN: First name in question doesn't match user name of that language, EN: Name is not in English in question_per_language, HE: First name in question doesn't match user name of that language, HE: Name is not in Hebrew in question_per_language",
        "Question_per_lang_errors: Message_id: 9, Channel: EMAIL, Profile_id: 50003385, Question_id: 30001, First name of user: Dima Hebrew name in profile: None, Question in database: en: ${{ to.first_name }} Are you coming to MuniExpo (Free)? - I'll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in database: he: ${{ to.first_name }} מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in Json: en: Dima Are you coming to MuniExpo (Free)? - I'll be glad meeting you. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, Question in Json: he: רשי מגיעים אירוע של MuniExpo? - לשלוח לך הזמנה? - אשמח לפגוש אתכם. https://faz4k77vi5.execute-api.us-east-1.amazonaws.com/play1/api/v1/smartlink/executeSmartlinkByIdentifier/8QqcT5GMDSDEaM3b3Kl0?isTestData=False, ERRORS: HE: First name in question doesn't match user name of that language, HE: No Hebrew Version of name in database",
        "Body_per_lang_errors: Message_id: 11, Channel: DEFAULT, Profile_id: 2, First name of user: Ido Hebrew name in profile: טל, Question_id: None, Message_template_text_block_id: 9010, body in table: {'en': '${{ to.first_name }} Best wishes or the Israeli 76th Independence Day', 'he': '${{ to.first_name }} צרור איחולים ליום העצמאות של ישראל'}, Body in en: טל Best wishes or the Israeli 76th Independence Day, Body in he: Ido צרור איחולים ליום העצמאות של ישראל, ERRORS: EN: First name in body_per_lang doesn't match user name of that language, EN: Name is not in English in body_per_language, HE: First name in body_per_lang doesn't match user name of that language, HE: Name is not in Hebrew in body_per_language",
        "Body_per_lang_errors: Message_id: 12, Channel: DEFAULT, Profile_id: 50003425, First name of user: PF_Deleted_or_Missing Hebrew name in profile: None, Question_id: None, Message_template_text_block_id: 9010, body in table: {'en': '${{ to.first_name }} Best wishes or the Israeli 76th Independence Day', 'he': '${{ to.first_name }} צרור איחולים ליום העצמאות של ישראל'}, Body in en: Ido Best wishes or the Israeli 76th Independence Day, Body in he: טל צרור איחולים ליום העצמאות של ישראל, ERRORS: User or Profile is missing or deleted",
        "Body_per_lang_errors: Message_id: 13, Channel: DEFAULT, Profile_id: 2, First name of user: Ido Hebrew name in profile: טל, Question_id: None, Message_template_text_block_id: 9010, body in table: {'en': '${{ to.first_name }} Best wishes or the Israeli 76th Independence Day', 'he': '${{ to.first_name }} צרור איחולים ליום העצמאות של ישראל'}, Body in en: Ido Best wishes o the Israeli 76th Independence Day, Body in he: טל צור איחולים ליום העצמאות של ישראל, ERRORS: EN: body_per_lang in Json doesn't match the body in the template, HE: body_per_lang in Json doesn't match the body in the template",
        "Body_per_lang_errors: Message_id: 14, Channel: DEFAULT, Profile_id: 2, First name of user: Ido Hebrew name in profile: טל, Question_id: None, Message_template_text_block_id: 9010, body in table: {'en': '${{ to.first_name }} Best wishes or the Israeli 76th Independence Day', 'he': '${{ to.first_name }} צרור איחולים ליום העצמאות של ישראל'}, Body in en: Io Best wishes or the Israeli 76th Independence Day, Body in he: ט צרור איחולים ליום העצמאות של ישראל, ERRORS: EN: First name in body_per_lang doesn't match user name of that language, HE: First name in body_per_lang doesn't match user name of that language",
        "Body_per_lang_errors: Message_id: 15, Channel: DEFAULT, Profile_id: 50003385, First name of user: Dima Hebrew name in profile: None, Question_id: None, Message_template_text_block_id: 9010, body in table: {'en': '${{ to.first_name }} Best wishes or the Israeli 76th Independence Day', 'he': '${{ to.first_name }} צרור איחולים ליום העצמאות של ישראל'}, Body in en: Dima Best wishes or the Israeli 76th Independence Day, Body in he: צרור איחולים ליום העצמאות של ישראל, ERRORS: HE: First name in body_per_lang doesn't match user name of that language, HE: No Hebrew Version of name in database",
        "EMPTY MESSAGE ERROR, MESSAGE_ID: 16",
    ]


def main():
    test_first_name_in_english_and_hebrew_does_not_match()


if __name__ == "__main__":
    main()
