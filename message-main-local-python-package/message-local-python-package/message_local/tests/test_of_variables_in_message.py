# TODO Accessing any fields of the effective profile/user/person or the recipient profile/user/person
#    - User Context
#    - fields from person/profile/user tables in the database
#    - logger_table
#    - fields from text_block_type (field_text_block_type.field_text_block_type_table)
#    - variable ...
#    both from Dialog Workflow and Message Text Block
#    We should create random value in each and make sure it was populated by the template package (currently in variable package)
#    The caller can't know what exists in all message_text_block nor workflow states in the future.
