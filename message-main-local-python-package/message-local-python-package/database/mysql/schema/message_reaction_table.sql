CREATE TABLE `message_reaction_table` (
  `message_reaction_id` int unsigned NOT NULL,
  `message_id` bigint unsigned NOT NULL,
  `reaction_id` smallint unsigned DEFAULT NULL,
  `created_user_id` bigint unsigned DEFAULT NULL,
  `profile_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`message_reaction_id`),
  UNIQUE KEY `message_reaction_id_UNIQUE` (`message_reaction_id`),
  K<PERSON>Y `fk_message_reaction_table_1_idx` (`reaction_id`),
  KEY `fk_message_reaction_table_2_idx` (`created_user_id`),
  KEY `message_reaction.message_id.fk` (`message_id`),
  KEY `message_reaction.profile_id.fk` (`profile_id`),
  CONSTRAINT `message_reaction.created_user_id.fk` FOREIGN KEY (`created_user_id`) REFERENCES `user`.`user_table` (`user_id`),
  CONSTRAINT `message_reaction.message_id.fk` FOREI<PERSON>N KEY (`message_id`) REFERENCES `message`.`message_table` (`message_id`),
  CONSTRAINT `message_reaction.profile_id.fk` FOREIGN KEY (`profile_id`) REFERENCES `profile`.`profile_table` (`profile_id`),
  CONSTRAINT `message_reaction.reaction_id.fk` FOREIGN KEY (`reaction_id`) REFERENCES `reaction`.`reaction_table` (`reaction_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
