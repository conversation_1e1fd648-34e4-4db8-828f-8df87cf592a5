CREATE 
    ALGORITHM = UNDEFINED 
    DEFINER = `bubbelz`@`%` 
    SQL SECURITY DEFINER
VIEW `message`.`message_outbox_view` AS
    SELECT 
        `message`.`message_id` AS `message_id`,
        `message`.`identifier` AS `identifier`,
        `message`.`number` AS `number`,
        `label_message`.`label_id` AS `label_id`,
        `message`.`queue_status_id` AS `queue_status_id`,
        `queue_status_ml`.`title` AS `queue_status_title`,
        `message`.`compound_message` AS `compound_message`,
        `message`.`compound_message_json` AS `compound_message_json`,
        `message`.`compound_message_json_version` AS `compound_message_json_version`,
        `message`.`process_id` AS `process_id`,
        `message`.`thread_id` AS `thread_id`,
        `message`.`user_jwt` AS `user_jwt`,
        `message`.`class_parameters_json` AS `class_parameters_json`,
        `message`.`function_parameters_json` AS `function_parameters_json`,
        `message`.`server_ip_v4` AS `server_ip_v4`,
        `message`.`server_ip_v6` AS `server_ip_v6`,
        `message`.`action_id` AS `action_id`,
        CAST(`message`.`scheduled_sent_timestamp` AS TIME) AS `exection_start_time`,
        NULL AS `exection_end_time`,
        `message`.`stdout` AS `stdout`,
        `message`.`stderr` AS `stderr`,
        `label_ml`.`title` AS `message_label_title`,
        `message`.`updated_timestamp` AS `updated_timestamp`,
        `message`.`return_code` AS `return_code`,
        `message`.`campaign_id` AS `campaign_id`,
        `message`.`is_test_data` AS `is_test_data`,
        `message`.`component_id` AS `component_id`,
        `message`.`created_timestamp` AS `created_timestamp`
    FROM
        (((`message`.`message_table` `message`
        JOIN `label_message`.`label_message_table` `label_message` ON ((`label_message`.`message_id` = `message`.`message_id`)))
        JOIN `queue`.`queue_status_ml_table` `queue_status_ml` ON ((`queue_status_ml`.`queue_status_id` = `message`.`queue_status_id`)))
        JOIN `label`.`label_ml_table` `label_ml` ON ((`label_ml`.`label_id` = `label_message`.`label_id`)))
    WHERE
        (`label_message`.`label_id` = 18)
