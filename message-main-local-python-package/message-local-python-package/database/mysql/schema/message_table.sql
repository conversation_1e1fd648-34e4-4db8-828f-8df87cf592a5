CREATE TABLE `message_table` (
  `message_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK: message_id',
  `number` bigint unsigned DEFAULT NULL,
  `identifier` varchar(255) DEFAULT NULL,
  `parent_message_id` bigint unsigned DEFAULT NULL,
  `message_message_relationship_id` smallint unsigned DEFAULT NULL COMMENT 'Reply\nAlternative Reply\nComment',
  `from_profile_id` bigint unsigned DEFAULT NULL,
  `from_email` varchar(255) DEFAULT NULL,
  `from_phone_number` varchar(255) DEFAULT NULL,
  `from_provider_id` smallint unsigned DEFAULT NULL,
  `from_user_external_id` int unsigned DEFAULT NULL,
  `from_system_id` smallint unsigned DEFAULT NULL,
  `conversation_id` int unsigned DEFAULT NULL,
  `to_phone_number` varchar(45) DEFAULT NULL,
  `to_provider_id` smallint unsigned DEFAULT NULL,
  `to_profile_id` bigint DEFAULT NULL,
  `to_profile_list_id` bigint unsigned DEFAULT NULL,
  `to_email` varchar(255) DEFAULT NULL,
  `cc_profile_id` bigint DEFAULT NULL,
  `cc_profile_list_id` bigint unsigned DEFAULT NULL,
  `cc_email` varchar(255) DEFAULT NULL,
  `bcc_profile_id` bigint DEFAULT NULL,
  `bcc_profile_list_id` bigint unsigned DEFAULT NULL,
  `bcc_email` varchar(255) DEFAULT NULL,
  `group_list_id` bigint unsigned DEFAULT NULL,
  `occurrence_id` bigint unsigned DEFAULT NULL,
  `location_id` bigint unsigned DEFAULT NULL,
  `message_channel_id` int unsigned DEFAULT NULL,
  `message_type_id` int unsigned DEFAULT NULL,
  `is_push_notification` tinyint(1) DEFAULT '0',
  `group_id` bigint unsigned DEFAULT NULL,
  `session_id` varchar(250) DEFAULT NULL,
  `visibility_id` bigint unsigned DEFAULT NULL,
  `is_pin` tinyint DEFAULT '0' COMMENT 'pin this message to the top',
  `mailbox_id` bigint unsigned DEFAULT NULL,
  `is_gen_ai` tinyint DEFAULT NULL,
  `is_dialog_workflow` tinyint DEFAULT NULL,
  `machine_learning_model_id` int unsigned DEFAULT NULL COMMENT 'TODO recommendation_engine_id?',
  `is_require_moderator` tinyint DEFAULT '1',
  `moderator_profile_id` bigint unsigned DEFAULT NULL,
  `moderator_feedback_type` int unsigned DEFAULT NULL,
  `moderator_feedback_text` text,
  `is_moderator_approved` tinyint DEFAULT '0',
  `queue_status_id` smallint unsigned DEFAULT NULL COMMENT 'For queue',
  `message_status_id` smallint unsigned DEFAULT NULL,
  `process_id` mediumint unsigned DEFAULT NULL COMMENT 'For queue',
  `stdout` text COMMENT 'For queue',
  `stderr` text COMMENT 'For queue\nany print inside the function',
  `return_code` int DEFAULT NULL COMMENT 'For queue\nindicating error',
  `returned_value` text COMMENT 'For queue\nthe value the function returned',
  `return_message` varchar(255) DEFAULT NULL COMMENT 'For queue',
  `profile_id` bigint unsigned DEFAULT NULL,
  `server_ip_v4` char(15) DEFAULT NULL,
  `server_ip_v6` char(45) DEFAULT NULL,
  `thread_id` int DEFAULT NULL,
  `user_jwt` varchar(255) DEFAULT NULL COMMENT 'user_jwt of  sender_profile_id so we can send the message on his behalf',
  `component_id` int DEFAULT NULL,
  `class_parameters_json` text,
  `function_parameters_json` text,
  `action_id` smallint unsigned DEFAULT NULL COMMENT 'Used by queue.push()',
  `campaign_id` int unsigned DEFAULT NULL,
  `campaign_criteria_set_id` bigint unsigned DEFAULT NULL,
  `requested_channel_id` smallint unsigned DEFAULT NULL,
  `actual_channel_id` smallint unsigned DEFAULT NULL,
  `email_message_id` varchar(998) DEFAULT NULL COMMENT 'From SMTP Server, Message-ID 998 characters',
  `body` text COMMENT 'Should be moved to ml table',
  `subject` varchar(255) DEFAULT NULL COMMENT 'Should be moved to ml table',
  `compound_message` text COMMENT 'Let''s move from compound_message to compound_message_json',
  `compound_message_json_version` varchar(45) DEFAULT NULL,
  `compound_message_json` json DEFAULT NULL COMMENT 'Let''s move from compound_message to compound_message_json',
  `session` varchar(45) DEFAULT NULL,
  `channel_id` int unsigned DEFAULT NULL,
  `last_dialog_workflow_state_id` bigint unsigned DEFAULT NULL,
  `next_dialog_workflow_state_id` int unsigned DEFAULT NULL,
  `from_subsystem_id` smallint unsigned DEFAULT NULL,
  `updated_effective_profile_id` bigint unsigned NOT NULL,
  `original_message_id` bigint unsigned DEFAULT NULL,
  `is_test_data` tinyint DEFAULT NULL COMMENT 'When admin what to test dialog on behalf of someone',
  `request_to_send_timestamp` timestamp NULL DEFAULT NULL COMMENT 'When the user asked to send - Pressend the Send/Send Later button',
  `scheduled_sent_timestamp` timestamp NULL DEFAULT NULL COMMENT 'If choosned "Send Later", when to send?',
  `actual_sent_timestamp` timestamp NULL DEFAULT NULL,
  `message_template_id` int unsigned DEFAULT NULL,
  `from_api_type_id` int unsigned DEFAULT NULL,
  `inforu_customer_id_old` varchar(255) DEFAULT NULL,
  `inforu_project_id_old` varchar(255) DEFAULT NULL,
  `inforu_channel_old` varchar(255) DEFAULT NULL,
  `inforu_type_old` varchar(255) DEFAULT NULL,
  `inforu_value_old` varchar(255) DEFAULT NULL,
  `inforu_keyword_old` varchar(255) DEFAULT NULL,
  `inforu_network_old` varchar(255) DEFAULT NULL,
  `inforu_short_code_old` varchar(255) DEFAULT NULL,
  `inforu_application_id_old` varchar(255) DEFAULT NULL,
  `inforu_customer_param_old` varchar(255) DEFAULT NULL,
  `inforu_mo_session_id_old` varchar(255) DEFAULT NULL,
  `inforu_incoming_json_old` text,
  `is_user_request` tinyint unsigned DEFAULT NULL,
  `user_request_price_id` bigint unsigned DEFAULT NULL,
  `start_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `end_timestamp` timestamp NULL DEFAULT NULL,
  `created_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_user_id` bigint unsigned NOT NULL,
  `created_real_user_id` bigint unsigned NOT NULL,
  `created_effective_user_id` bigint unsigned NOT NULL,
  `created_effective_profile_id` bigint unsigned NOT NULL,
  `updated_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_user_id` bigint unsigned NOT NULL,
  `updated_real_user_id` bigint unsigned NOT NULL,
  `updated_effective_user_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`message_id`),
  UNIQUE KEY `message_id_UNIQUE` (`message_id`),
  KEY `message.parent_message_id.idx` (`parent_message_id`),
  KEY `message.message_id.idx` (`message_id`),
  KEY `message.mailbox_id.mailbox_table_idx` (`mailbox_id`),
  KEY `message.sender_profile_id.fk` (`from_profile_id`),
  KEY `message.to_profile_list_id.fk_idx` (`to_profile_list_id`),
  KEY `message.message_channel_id.fk` (`message_channel_id`),
  KEY `message.location_id.fk` (`location_id`),
  KEY `message.visibility_id.fk` (`visibility_id`),
  KEY `message.message_type_id.fk` (`message_type_id`),
  KEY `message_table.message_status_id_idx` (`message_status_id`),
  KEY `message.source_subsystem_id_idx` (`from_subsystem_id`),
  CONSTRAINT `message_table.location_id.fk` FOREIGN KEY (`location_id`) REFERENCES `location`.`location_table` (`location_id`),
  CONSTRAINT `message_table.mailbox_id.fk` FOREIGN KEY (`mailbox_id`) REFERENCES `mailbox`.`mailbox_table` (`mailbox_id`),
  CONSTRAINT `message_table.messae_type_id.fk` FOREIGN KEY (`message_type_id`) REFERENCES `message_type_table` (`message_type_table_id`),
  CONSTRAINT `message_table.message_channel_id.fk` FOREIGN KEY (`message_channel_id`) REFERENCES `message_channel_table` (`message_channel_id`),
  CONSTRAINT `message_table.message_status_id` FOREIGN KEY (`message_status_id`) REFERENCES `message_status_type_table` (`message_status_id`),
  CONSTRAINT `message_table.sender_profile_id.fk` FOREIGN KEY (`from_profile_id`) REFERENCES `profile`.`profile_table` (`profile_id`),
  CONSTRAINT `message_table.to_profile_list_id.fk` FOREIGN KEY (`to_profile_list_id`) REFERENCES `profile`.`profile_list_table` (`profile_list_id`),
  CONSTRAINT `message_table.visibility_id.fk` FOREIGN KEY (`visibility_id`) REFERENCES `visibility`.`visibility_table` (`visibility_id`),
  CONSTRAINT `source_subsystem_id` FOREIGN KEY (`from_subsystem_id`) REFERENCES `system`.`subsystem_table` (`subsystem_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4862 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
