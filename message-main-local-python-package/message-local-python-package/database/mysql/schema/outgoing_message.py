from database_mysql_local.connector import Connector

conn = Connector.connect(schema_name="message")


def create_outgoing_message_table():
    cursor = conn.cursor()

    # Create the outgoing_message table
    create_table_query = """
        CREATE TABLE IF NOT EXISTS outgoing_message (
            id BIGINT UNSIGNED AUTO_INCREMENT,
            timestamp TIMESTAMP NOT NULL,
            from_profile_id BIGINT NOT NULL,
            to_profile_id BIGINT NOT NULL,
            cc_profile_id BIGINT NOT NULL,
            bcc_profile_id BIGINT NOT NULL,
            scheduled_timestamp TIMESTAMP,
            actual_timestamp TIMESTAMP,
            message_type_id INT UNSIGNED,
            message_channel_id INT UNSIGNED,
            sending_message_result_id INT UNSIGNED,
            PRIMARY KEY(id),
            FOREIGN KEY(from_profile_id) REFERENCES profile.profile(id),
            FOREIGN KEY(to_profile_id) REFERENCES profile.profile(id),
            FOREI<PERSON><PERSON> KEY(cc_profile_id) REFERENCES profile.profile(id),
            <PERSON><PERSON><PERSON><PERSON><PERSON> KEY(bcc_profile_id) REFERENCES profile.profile(id)
        )
    """
    cursor.execute(create_table_query)

    # commit change and close the database connection
    conn.commit()
    cursor.close()
    conn.close()
