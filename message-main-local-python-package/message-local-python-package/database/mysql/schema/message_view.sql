CREATE 
    ALGORITHM = UNDEFINED 
    DEFINER = `bubbelz`@`%` 
    SQL SECURITY DEFINER
VIEW `message`.`message_view` AS
    SELECT 
        `message`.`message_id` AS `message_id`,
        `message`.`number` AS `number`,
        `message`.`identifier` AS `identifier`,
        `message`.`parent_message_id` AS `parent_message_id`,
        `message`.`message_message_relationship_id` AS `message_message_relationship_id`,
        `message`.`from_profile_id` AS `from_profile_id`,
        `message`.`from_email` AS `from_email`,
        `message`.`from_phone_number` AS `from_phone_number`,
        `message`.`from_provider_id` AS `from_provider_id`,
        `message`.`from_user_external_id` AS `from_user_external_id`,
        `message`.`from_system_id` AS `from_system_id`,
        `message`.`conversation_id` AS `conversation_id`,
        `message`.`to_phone_number` AS `to_phone_number`,
        `message`.`to_provider_id` AS `to_provider_id`,
        `message`.`to_profile_id` AS `to_profile_id`,
        `message`.`to_profile_list_id` AS `to_profile_list_id`,
        `message`.`to_email` AS `to_email`,
        `message`.`cc_profile_id` AS `cc_profile_id`,
        `message`.`cc_profile_list_id` AS `cc_profile_list_id`,
        `message`.`cc_email` AS `cc_email`,
        `message`.`bcc_profile_id` AS `bcc_profile_id`,
        `message`.`bcc_profile_list_id` AS `bcc_profile_list_id`,
        `message`.`bcc_email` AS `bcc_email`,
        `message`.`group_list_id` AS `group_list_id`,
        `message`.`occurrence_id` AS `occurrence_id`,
        `message`.`location_id` AS `location_id`,
        `message`.`message_channel_id` AS `message_channel_id`,
        `message`.`message_type_id` AS `message_type_id`,
        `message`.`is_push_notification` AS `is_push_notification`,
        `message`.`group_id` AS `group_id`,
        `message`.`session_id` AS `session_id`,
        `message`.`visibility_id` AS `visibility_id`,
        `message`.`is_pin` AS `is_pin`,
        `message`.`mailbox_id` AS `mailbox_id`,
        `message`.`is_gen_ai` AS `is_gen_ai`,
        `message`.`machine_learning_model_id` AS `machine_learning_model_id`,
        `message`.`is_require_moderator` AS `is_require_moderator`,
        `message`.`moderator_profile_id` AS `moderator_profile_id`,
        `message`.`moderator_feedback_type` AS `moderator_feedback_type`,
        `message`.`moderator_feedback_text` AS `moderator_feedback_text`,
        `message`.`is_moderator_approved` AS `is_moderator_approved`,
        `message`.`queue_status_id` AS `queue_status_id`,
        `message`.`message_status_id` AS `message_status_id`,
        `message`.`process_id` AS `process_id`,
        `message`.`stdout` AS `stdout`,
        `message`.`stderr` AS `stderr`,
        `message`.`return_code` AS `return_code`,
        `message`.`returned_value` AS `returned_value`,
        `message`.`return_message` AS `return_message`,
        `message`.`profile_id` AS `profile_id`,
        `message`.`server_ip_v4` AS `server_ip_v4`,
        `message`.`server_ip_v6` AS `server_ip_v6`,
        `message`.`thread_id` AS `thread_id`,
        `message`.`user_jwt` AS `user_jwt`,
        `message`.`class_parameters_json` AS `class_parameters_json`,
        `message`.`function_parameters_json` AS `function_parameters_json`,
        `message`.`action_id` AS `action_id`,
        `message`.`campaign_id` AS `campaign_id`,
        `message`.`campaign_criteria_set_id` AS `campaign_criteria_set_id`,
        `message`.`requested_channel_id` AS `requested_channel_id`,
        `message`.`actual_channel_id` AS `actual_channel_id`,
        `message`.`email_message_id` AS `email_message_id`,
        `message`.`body` AS `body`,
        `message`.`subject` AS `subject`,
        `message`.`compound_message` AS `compound_message`,
        `message`.`compound_message_json_version` AS `compound_message_json_version`,
        `message`.`compound_message_json` AS `compound_message_json`,
        `message`.`session` AS `session`,
        `message`.`channel_id` AS `channel_id`,
        `message`.`last_dialog_workflow_state_id` AS `last_dialog_workflow_state_id`,
        `message`.`next_dialog_workflow_state_id` AS `next_dialog_workflow_state_id`,
        `message`.`from_subsystem_id` AS `from_subsystem_id`,
        `message`.`updated_effective_profile_id` AS `updated_effective_profile_id`,
        `message`.`original_message_id` AS `original_message_id`,
        `message`.`is_test_data` AS `is_test_data`,
        `message`.`request_to_send_timestamp` AS `request_to_send_timestamp`,
        `message`.`scheduled_sent_timestamp` AS `scheduled_sent_timestamp`,
        `message`.`actual_sent_timestamp` AS `actual_sent_timestamp`,
        `message`.`message_template_id` AS `message_template_id`,
        `message`.`from_api_type_id` AS `from_api_type_id`,
        `message`.`inforu_customer_id_old` AS `inforu_customer_id_old`,
        `message`.`inforu_project_id_old` AS `inforu_project_id_old`,
        `message`.`inforu_channel_old` AS `inforu_channel_old`,
        `message`.`inforu_type_old` AS `inforu_type_old`,
        `message`.`inforu_value_old` AS `inforu_value_old`,
        `message`.`inforu_keyword_old` AS `inforu_keyword_old`,
        `message`.`inforu_network_old` AS `inforu_network_old`,
        `message`.`inforu_short_code_old` AS `inforu_short_code_old`,
        `message`.`inforu_application_id_old` AS `inforu_application_id_old`,
        `message`.`inforu_customer_param_old` AS `inforu_customer_param_old`,
        `message`.`inforu_mo_session_id_old` AS `inforu_mo_session_id_old`,
        `message`.`inforu_incoming_json_old` AS `inforu_incoming_json_old`,
        `message`.`start_timestamp` AS `start_timestamp`,
        `message`.`end_timestamp` AS `end_timestamp`,
        `message`.`created_timestamp` AS `created_timestamp`,
        `message`.`created_user_id` AS `created_user_id`,
        `message`.`created_real_user_id` AS `created_real_user_id`,
        `message`.`created_effective_user_id` AS `created_effective_user_id`,
        `message`.`created_effective_profile_id` AS `created_effective_profile_id`,
        `message`.`updated_timestamp` AS `updated_timestamp`,
        `message`.`updated_user_id` AS `updated_user_id`,
        `message`.`updated_real_user_id` AS `updated_real_user_id`,
        `message`.`updated_effective_user_id` AS `updated_effective_user_id`,
        `message`.`component_id` AS `component_id`
    FROM
        `message`.`message_table` `message`
