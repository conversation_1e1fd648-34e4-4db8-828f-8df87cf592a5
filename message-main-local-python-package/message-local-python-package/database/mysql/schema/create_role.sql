CREATE ROLE IF NOT EXISTS message_local;
GRANT SELECT ON person.* TO message_local;
-- profile?
GRANT SELECT ON campaign_message_template.* TO 'message_local';
GRANT SELECT ON message_template.* TO message_local;
GRANT SELECT ON message_template_general.* TO 'message_local';

-- I Don't think we has this schema
-- GRANT SELECT ON criteria_set_profile.* TO 'message_local';
GRANT SELECT ON criteria_profile.* TO 'message_local';
GRANT SELECT ON criteria.* TO 'message_local';

GRANT 'message_local' TO 'eli.p';
GRANT 'message_local' TO 'david.w';

SHOW GRANTS FOR `message_local`@`%`;

SELECT DISTINCT User 'Role Name', if(from_user is NULL,0, 1) Active 
       FROM mysql.user LEFT JOIN role_edges ON from_user=user 
       WHERE account_locked='Y' AND password_expired='Y' AND authentication_string='';

SHOW GRANTS FOR `api_local`@`%`;

SHOW GRANTS FOR `eli.p`@localhost;
SHOW GRANTS FOR `eli.p`@`%`;

SHOW GRANTS 
FOR `eli.p`@`%` 
USING api_local;

SELECT current_role();

SET DEFAULT ROLE ALL TO crm_read1@localhost;
