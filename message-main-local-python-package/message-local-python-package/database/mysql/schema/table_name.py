from database_mysql_local.connector import Connector

# We should have in /db folder Python file per each table
# (with the exception of XXX_ml_table that will be in the same Python file as the XXX_table),
# the filename will be the table name

# We should create a generic class to be used for create, upgrade or downgrade each table
# The new class should update the database schema (i.e. dependencies) in the master database
# The package should use "Environment" Environment variable and the URL package determine the hostname
# The package should use "MasterDB_*" to get the credentials for the master database

conn = Connector.connect(schema_name="message")


def create_message_channel_table():
    cursor = conn.cursor()

    # Create schema if not exists
    cursor.execute("CREATE SCHEMA IF NOT EXISTS message")
    cursor.execute("USE message")

    # Create message table
    message_channel_create_table_ddl = """
    CREATE TABLE IF NOT EXISTS message_channel (
    id BIGINT UNSIGNED AUTO_INCREMENT NOT NULL,
    provider_id BIGINT UNSIGNED,
    PRIMARY KEY(id),
    FOREIG<PERSON> KEY(provider_id) REFERENCES provider(id)
    )
    """
    cursor.execute(message_channel_create_table_ddl)

    message_channel_ml_create_table_ddl = """
    CREATE TABLE IF NOT EXISTS message_ml (
    id BIGINT UNSIGNED AUTO_INCREMENT,
    message_channel BIGINT UNSIGNED NOT NULL,
    provider_id BIGINT UNSIGNED,
    lang_code CHAR(5),
    PRIMARY KEY(id),
    FOREIGN KEY(message_channel) REFERENCES message_channel(id),
    FOREIGN KEY(provider_id) REFERENCES provider(id)
    );
    """
    cursor.execute(message_channel_ml_create_table_ddl)

    conn.commit()
    cursor.close()
    conn.close()
