from database_mysql_local.connector import Connector

conn = Connector.connect(schema_name="message")


def create_provider_table():
    cursor = conn.cursor()

    # Create the provider table
    create_table_query = """
        CREATE TABLE IF NOT EXISTS provider (
            id BIGINT UNSIGNED AUTO_INCREMENT,
            provider_name VARCHAR(256) NULL,
            PRIMARY KEY(id)
        )
    """
    cursor.execute(create_table_query)

    create_table_query = """
        CREATE TABLE IF NOT EXISTS provider_ml (
            id BIGINT UNSIGNED AUTO_INCREMENT,
            provider_id BIGINT UNSIGNED,
            lang_code CHAR(5),
            provider_name VARCHAR(256) NULL,
            PRIMARY KEY(id),
            FOREIGN KEY(provider_id) REFERENCES provider(id)
        )
    """
    cursor.execute(create_table_query)

    cursor.execute("CREATE INDEX idx_provider_id_lang_code ON provider_ml (provider_id, lang_code);")

    # commit change and Close the database connection
    conn.commit()
    cursor.close()
    conn.close()
