CREATE 
    ALGORITHM = UNDEFINED 
    DEFINER = `bubbelz`@`%` 
    SQL SECURITY DEFINER
VIEW `message`.`message_whatapp_general_view` AS
    SELECT 
        `message`.`message`.`message_id` AS `message_id`,
        `message`.`message`.`number` AS `number`,
        `message`.`message`.`identifier` AS `identifier`,
        `message`.`message`.`parent_message_id` AS `parent_message_id`,
        `message`.`message`.`message_message_relationship_id` AS `message_message_relationship_id`,
        `message`.`message`.`sender_profile_id` AS `sender_profile_id`,
        `message`.`message`.`sender_email` AS `sender_email`,
        `message`.`message`.`to_profile_id` AS `to_profile_id`,
        `message`.`message`.`to_profile_list_id` AS `to_profile_list_id`,
        `message`.`message`.`to_email` AS `to_email`,
        `message`.`message`.`cc_profile_id` AS `cc_profile_id`,
        `message`.`message`.`cc_profile_list_id` AS `cc_profile_list_id`,
        `message`.`message`.`cc_email` AS `cc_email`,
        `message`.`message`.`bcc_profile_id` AS `bcc_profile_id`,
        `message`.`message`.`bcc_profile_list_id` AS `bcc_profile_list_id`,
        `message`.`message`.`bcc_email` AS `bcc_email`,
        `message`.`message`.`group_list_id` AS `group_list_id`,
        `message`.`message`.`occurrence_id` AS `occurrence_id`,
        `message`.`message`.`location_id` AS `location_id`,
        `message`.`message`.`message_channel_id` AS `message_channel_id`,
        `message`.`message`.`message_type_id` AS `message_type_id`,
        `message`.`message`.`is_push_notification` AS `is_push_notification`,
        `message`.`message`.`group_id` AS `group_id`,
        `message`.`message`.`session_id` AS `session_id`,
        `message`.`message`.`visibility_id` AS `visibility_id`,
        `message`.`message`.`is_pin` AS `is_pin`,
        `message`.`message`.`mailbox_id` AS `mailbox_id`,
        `message`.`message`.`is_gen_ai` AS `is_gen_ai`,
        `message`.`message`.`machine_learning_model_id` AS `machine_learning_model_id`,
        `message`.`message`.`is_require_moderator` AS `is_require_moderator`,
        `message`.`message`.`moderator_profile_id` AS `moderator_profile_id`,
        `message`.`message`.`moderator_feedback_type` AS `moderator_feedback_type`,
        `message`.`message`.`moderator_feedback_text` AS `moderator_feedback_text`,
        `message`.`message`.`is_moderator_approved` AS `is_moderator_approved`,
        `message`.`message`.`queue_status_id` AS `queue_status_id`,
        `message`.`message`.`message_status_id` AS `message_status_id`,
        `message`.`message`.`process_id` AS `process_id`,
        `message`.`message`.`stdout` AS `stdout`,
        `message`.`message`.`stderr` AS `stderr`,
        `message`.`message`.`return_code` AS `return_code`,
        `message`.`message`.`returned_value` AS `returned_value`,
        `message`.`message`.`return_message` AS `return_message`,
        `message`.`message`.`profile_id` AS `profile_id`,
        `message`.`message`.`server_ip_v4` AS `server_ip_v4`,
        `message`.`message`.`server_ip_v6` AS `server_ip_v6`,
        `message`.`message`.`thread_id` AS `thread_id`,
        `message`.`message`.`user_jwt` AS `user_jwt`,
        `message`.`message`.`class_parameters_json` AS `class_parameters_json`,
        `message`.`message`.`function_parameters_json` AS `function_parameters_json`,
        `message`.`message`.`action_id` AS `action_id`,
        `message`.`message`.`campaign_id` AS `campaign_id`,
        `message`.`message`.`campaign_criteria_set_id` AS `campaign_criteria_set_id`,
        `message`.`message`.`requested_channel_id` AS `requested_channel_id`,
        `message`.`message`.`actual_channel_id` AS `actual_channel_id`,
        `message`.`message`.`from_provider_id` AS `from_provider_id`,
        `message`.`message`.`to_provider_id` AS `to_provider_id`,
        `message`.`message`.`email_message_id` AS `email_message_id`,
        `message`.`message`.`body` AS `body`,
        `message`.`message`.`subject` AS `subject`,
        `message`.`message`.`compound_message` AS `compound_message`,
        `message`.`message`.`compound_message_json_version` AS `compound_message_json_version`,
        `message`.`message`.`compound_message_json` AS `compound_message_json`,
        `message`.`message`.`session` AS `session`,
        `message`.`message`.`channel_id` AS `channel_id`,
        `message`.`message`.`dialog_workflow_state_id` AS `dialog_workflow_state_id`,
        `message`.`message`.`source_subsystem_id` AS `source_subsystem_id`,
        `message`.`message`.`updated_effective_profile_id` AS `updated_effective_profile_id`,
        `message`.`message`.`original_message_id` AS `original_message_id`,
        `message`.`message`.`conversation_id` AS `conversation_id`,
        `message`.`message`.`is_test_data` AS `is_test_data`,
        `message`.`message`.`request_to_send_timestamp` AS `request_to_send_timestamp`,
        `message`.`message`.`scheduled_sent_timestamp` AS `scheduled_sent_timestamp`,
        `message`.`message`.`actual_sent_timestamp` AS `actual_sent_timestamp`,
        `message`.`message`.`message_template_id` AS `message_template_id`,
        `message`.`message`.`inforu_customer_id` AS `inforu_customer_id`,
        `message`.`message`.`inforu_project_id` AS `inforu_project_id`,
        `message`.`message`.`inforu_channel` AS `inforu_channel`,
        `message`.`message`.`inforu_type` AS `inforu_type`,
        `message`.`message`.`inforu_value` AS `inforu_value`,
        `message`.`message`.`inforu_keyword` AS `inforu_keyword`,
        `message`.`message`.`inforu_network` AS `inforu_network`,
        `message`.`message`.`inforu_short_code` AS `inforu_short_code`,
        `message`.`message`.`inforu_application_id` AS `inforu_application_id`,
        `message`.`message`.`inforu_customer_param` AS `inforu_customer_param`,
        `message`.`message`.`inforu_mo_session_id` AS `inforu_mo_session_id`,
        `message`.`message`.`inforu_incoming_json` AS `inforu_incoming_json`,
        `message`.`message`.`start_timestamp` AS `start_timestamp`,
        `message`.`message`.`end_timestamp` AS `end_timestamp`,
        `message`.`message`.`created_timestamp` AS `created_timestamp`,
        `message`.`message`.`created_user_id` AS `created_user_id`,
        `message`.`message`.`created_real_user_id` AS `created_real_user_id`,
        `message`.`message`.`created_effective_user_id` AS `created_effective_user_id`,
        `message`.`message`.`created_effective_profile_id` AS `created_effective_profile_id`,
        `message`.`message`.`updated_timestamp` AS `updated_timestamp`,
        `message`.`message`.`updated_user_id` AS `updated_user_id`,
        `message`.`message`.`updated_real_user_id` AS `updated_real_user_id`,
        `message`.`message`.`updated_effective_user_id` AS `updated_effective_user_id`
    FROM
        `message`.`message_general_view` `message`
    WHERE
        (`message`.`message`.`actual_channel_id` = 11)
