CREATE 
VIEW `form`.`form_general_view` AS
    SELECT 
        `form`.`form_id` AS `form_id`,
        `form`.`name` AS `form_name`,
        `form_message_template`.`min_message_template` AS `min_message_template`,
        `form_message_template`.`max_message_template` AS `max_message_template`,
        `form_message_template`.`page` AS `form_page`,
        `form_message_template`.`seq` AS `form_message_seq_in_page`,
        `form_message_template`.`form_message_template_id` AS `form_message_template_id`,
        `form_message_template`.`message_template_id` AS `message_template_id`,
        `message_template`.`message_template_general`.`message_template_name` AS `message_template_name`,
        `message_template`.`message_template_general`.`message_template_text_block_seq` AS `message_template_text_block_seq`,
        `message_template`.`message_template_general`.`message_template_text_block_id` AS `message_template_text_block_id`,
        `message_template`.`message_template_general`.`message_template_text_block_name` AS `message_template_text_block_name`,
        `message_template`.`message_template_general`.`default_subject_template` AS `default_subject_template`,
        `message_template`.`message_template_general`.`default_body_template` AS `default_body_template`,
        `message_template`.`message_template_general`.`message_template_text_block_ml_lang_code` AS `message_template_text_block_ml_lang_code`,
        `message_template`.`message_template_general`.`question_id` AS `question_id`,
        `message_template`.`message_template_general`.`question.title` AS `question_title`,
        `message_template`.`message_template_general`.`question_is_required` AS `question_is_required`,
        `message_template`.`message_template_general`.`question_ml_lang_code` AS `question_ml_lang_code`,
        `message_template`.`message_template_general`.`default_question_possible_answer_id` AS `default_question_possible_answer_id`,
        `message_template`.`message_template_general`.`schema_attributes` AS `schema_attribute`,
        `message_template`.`message_template_general`.`uischema_attributes` AS `uischema_attribute`,
        `message_template`.`message_template_general`.`question_type_id` AS `question_type_id`,
        `message_template`.`message_template_general`.`question_type_name` AS `question_type_name`,
        `message_template`.`message_template_general`.`question_type_uischema_attributes` AS `question_type_uischema_attributes`,
        `message_template`.`message_template_general`.`question_type_schema_attributes` AS `question_type_schema_attributes`,
        `message_template`.`message_template_general`.`variable_id` AS `variable_id`,
        `message_template`.`message_template_general`.`variable_name` AS `variable_name`,
        `message_template`.`message_template_general`.`variable_ml_title` AS `variable_ml_title`,
        `message_template`.`message_template_general`.`field_name` AS `field_name`,
        `message_template`.`message_template_general`.`message_template_text_block_is_visible` AS `message_template_text_block_is_visible`,
        `message_template`.`message_template_general`.`possible_answer` AS `possible_answer`,
        `message_template`.`message_template_general`.`message_template_text_block.criteria_set_id` AS `message_template_text_block.criteria_set_id`
    FROM
        ((`form`.`form_table` `form`
        LEFT JOIN `form`.`form_message_template_table` `form_message_template` ON ((`form_message_template`.`form_id` = `form`.`form_id`)))
        LEFT JOIN `message_template`.`message_template_general_view` `message_template_general` ON ((`message_template`.`message_template_general`.`message_template_id` = `form_message_template`.`message_template_id`)))
    ORDER BY `form`.`form_id` , `form_message_template`.`page` , `form_message_template`.`seq` , `message_template`.`message_template_general`.`message_template_text_block_seq`