USE message;
CREATE TABLE `message_status`
(
    `message_status_id` smallint unsigned NOT NULL,
    PRIMARY KEY (`message_status_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

CREATE TABLE `message_status_ml`
(
    `message_status_ml_id` int unsigned      NOT NULL AUTO_INCREMENT COMMENT 'PK: message_channel_ml_id',
    `message_status_id`    smallint unsigned NOT NULL,
    `lang_code`            char(5)                    DEFAULT NULL,
    `title`                varchar(250)               DEFAULT NULL,
    `created_timestamp`    timestamp         NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `created_user_id`      bigint unsigned   NOT NULL,
    `updated_timestamp`    timestamp         NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_user_id`      bigint unsigned   NOT NULL,
    `start_timestamp`      timestamp         NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `end_timestamp`        timestamp         NULL     DEFAULT NULL,
    PR<PERSON>AR<PERSON> KEY (`message_status_ml_id`),
    <PERSON><PERSON>Y `message_status_id` (`message_status_id`
        ),
    CONSTRAINT `message_status_ml_ibfk_1` FOREIGN KEY (`message_status_id`) REFERENCES `message_status` (`message_status_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;
