CREATE TABLE message.`message_inforu_table` (
  `message_id` bigint unsigned NOT NULL COMMENT 'PK: message_id',
  `inforu_customer_id` varchar(255) DEFAULT NULL,
  `inforu_project_id` varchar(255) DEFAULT NULL,
  `inforu_channel` varchar(255) DEFAULT NULL,
  `inforu_type` varchar(255) DEFAULT NULL,
  `inforu_value` varchar(255) DEFAULT NULL,
  `inforu_keyword` varchar(255) DEFAULT NULL,
  `inforu_network` varchar(255) DEFAULT NULL,
  `inforu_short_code` varchar(255) DEFAULT NULL,
  `inforu_application_id` varchar(255) DEFAULT NULL,
  `inforu_customer_param` varchar(255) DEFAULT NULL,
  `inforu_mo_session_id` varchar(255) DEFAULT NULL,
  `inforu_incoming_json` text,
  PRIMARY KEY (`message_id`),
  UNIQUE KEY `message_id_UNIQUE` (`message_id`),
  CONSTRAINT `message_table.message_id.fk` F<PERSON><PERSON><PERSON><PERSON> KEY (`message_id`) REFERENCES `message`.`message_table` (`message_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4484 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
