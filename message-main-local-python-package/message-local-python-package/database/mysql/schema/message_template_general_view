CREATE 

VIEW `message_template`.`message_template_general_view` AS
    SELECT 
        `message_template`.`message_template_id` AS `message_template_id`,
        `message_template`.`name` AS `message_template_name`,
        `message_template`.`message_template_text_block`.`criteria_id_old` AS `message_template_text_block.criteria_id_old`,
        `criteria`.`criteria`.`name` AS `message_template_text_block.criteria.name`,
        `message_template`.`message_template_text_block`.`message_template_text_block_id` AS `message_template_text_block_id`,
        `message_template`.`message_template_text_block`.`name` AS `message_template_text_block_name`,
        `message_template`.`message_template_text_block`.`is_visible` AS `message_template_text_block_is_visible`,
        `message_template`.`message_template_text_block`.`criteria_set_id` AS `message_template_text_block.criteria_set_id`,
        `message_template`.`message_template_message_template_text_block`.`seq` AS `message_template_text_block_seq`,
        `message_template`.`message_template_text_block`.`question_id` AS `question_id`,
        `question`.`question`.`default_question_possible_answer_id` AS `default_question_possible_answer_id`,
        `question`.`question`.`is_required` AS `question_is_required`,
        `message_template`.`message_template_text_block_ml`.`lang_code` AS `message_template_text_block_ml_lang_code`,
        `message_template`.`message_template_text_block_ml`.`default_subject_template` AS `default_subject_template`,
        `message_template`.`message_template_text_block_ml`.`default_body_template` AS `default_body_template`,
        `message_template`.`message_template_text_block_ml`.`email_subject_template` AS `email_subject_template`,
        `message_template`.`message_template_text_block_ml`.`email_body_html_template` AS `email_body_html_template`,
        `message_template`.`message_template_text_block_ml`.`sms_body_template` AS `sms_body_template`,
        `message_template`.`message_template_text_block_ml`.`whatsapp_body_template` AS `whatsapp_body_template`,
        `message_template`.`message_template_text_block_type_ml`.`title` AS `message_template_text_block_type.title`,
        `question`.`question_ml`.`title` AS `question.title`,
        `question_type`.`question_type`.`question_type_id` AS `question_type_id`,
        `question_type`.`question_type`.`name` AS `question_type_name`,
        `question`.`question_ml`.`lang_code` AS `question_ml_lang_code`,
        `question_type`.`question_type`.`schema_attributes` AS `question_type_schema_attributes`,
        `question_type`.`question_type`.`uischema_attributes` AS `question_type_uischema_attributes`,
        `question`.`question`.`schema_attributes` AS `schema_attributes`,
        `question`.`question`.`uischema_attributes` AS `uischema_attributes`,
        `question`.`question`.`hint` AS `question_hint`,
        `field`.`variable`.`variable_id` AS `variable_id`,
        `field`.`variable`.`name` AS `variable_name`,
        `field`.`variable_ml`.`title` AS `variable_ml_title`,
        `field`.`field`.`field_id` AS `field_id`,
        `field`.`field`.`name` AS `field_name`,
        `question`.`question_possible_answer_ml`.`value` AS `possible_answer`,
        `question`.`question_possible_answer_ml`.`action_id` AS `action_id`,
        `action`.`action`.`name` AS `action_name`
    FROM
        ((((((((((((((`message_template`.`message_template_table` `message_template`
        LEFT JOIN `message_template`.`message_template_message_template_text_block_table` `message_template_message_template_text_block` ON ((`message_template`.`message_template_message_template_text_block`.`message_template_id` = `message_template`.`message_template`.`message_template_id`)))
        LEFT JOIN `message_template`.`message_template_text_block_table` `message_template_text_block` ON ((`message_template`.`message_template_text_block`.`message_template_text_block_id` = `message_template`.`message_template_message_template_text_block`.`message_template_text_block_id`)))
        LEFT JOIN `message_template`.`message_template_text_block_ml_table` `message_template_text_block_ml` ON ((`message_template`.`message_template_text_block`.`message_template_text_block_id` = `message_template`.`message_template_text_block_ml`.`message_template_text_block_id`)))
        LEFT JOIN criteria.criteria_set_table criteria_set ON criteria_set.criteria_set_id=message_template_text_block.criteria_set_id
        LEFT JOIN `criteria`.`criteria_table` `criteria` ON `criteria`.`criteria`.`criteria_id` = criteria_set.criteria_id
        LEFT JOIN `message_template`.`message_template_text_block_type_ml_table` `message_template_text_block_type_ml` ON ((`message_template`.`message_template_text_block_type_ml`.`message_template_text_block_type_id` = `message_template`.`message_template_text_block`.`message_template_text_block_type_id`)))
        LEFT JOIN `question`.`question_table` `question` ON ((`question`.`question`.`question_id` = `message_template`.`message_template_text_block`.`question_id`)))
        LEFT JOIN `question`.`question_ml_table` `question_ml` ON ((`question`.`question_ml`.`question_id` = `message_template`.`message_template_text_block`.`question_id`)))
        LEFT JOIN `question_type`.`question_type_table` `question_type` ON ((`question_type`.`question_type`.`question_type_id` = `question`.`question`.`question_type_id`)))
        LEFT JOIN `question`.`question_possible_answer_table` `question_possible_answer` ON ((`question`.`question_possible_answer`.`question_id` = `message_template`.`message_template_text_block`.`question_id`)))
        LEFT JOIN `question`.`question_possible_answer_ml_table` `question_possible_answer_ml` ON ((`question`.`question_possible_answer_ml`.`question_possible_answer_id` = `question`.`question_possible_answer`.`question_possible_answer_id`)))
        LEFT JOIN `field`.`variable_table` `variable` ON ((`field`.`variable`.`variable_id` = `question`.`question`.`variable_id`)))
        LEFT JOIN `field`.`field_table` `field` ON ((`field`.`field`.`field_id` = `field`.`variable`.`field_id`)))
        LEFT JOIN `action`.`action_table` `action` ON ((`action`.`action`.`action_id` = `question`.`question_possible_answer_ml`.`action_id`)))
        LEFT JOIN `field`.`variable_ml_table` `variable_ml` ON (((`field`.`variable_ml`.`variable_id` = `field`.`variable`.`variable_id`)
            AND (`field`.`variable_ml`.`lang_code` = 'en')))))
    ORDER BY `message_template`.`message_template`.`message_template_id` , `message_template`.`message_template_message_template_text_block`.`seq`