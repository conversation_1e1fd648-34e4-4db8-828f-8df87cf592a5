-- Purpose: This report present all the imports done do the system group by each source (uncomment the example below if needed)

-- TODO: Please write CREATE VIEWs (if it's relevant to your development) in new files and afterward delete this file. File name is exactly as the name of the view.

-- If you need to add parameter to the SQL
-- https://stackoverflow.com/questions/2281890/can-i-create-view-with-parameter-in-mysql

-- Must have USE statement where the view should be created
-- USE importer;

-- Name of the view should be meaningful and according to our standard naming convention
-- DROP VIEW entity_types_imported_group_by_source_view;
-- CREATE VIEW IF NOT EXISTS entity_types_imported_group_by_source_view AS
--  SELECT entity_type_name AS 'Entity Type', source_name AS 'Website', count(*) as 'Number of Entries'
--    FROM  importer.importer_table
--    JOIN entity_type.entity_type_ml_table ON entity_type.entity_type_ml_table.entity_type_id=importer.importer_table.entity_type_id
--    JOIN source.source_ml_table ON source.source_ml_table.source_id=importer.importer_table.source_id
--    GROUP BY source.source_ml_table.source_name;

-- SELECT * FROM entity_types_imported_group_by_source_view LIMIT 100;
