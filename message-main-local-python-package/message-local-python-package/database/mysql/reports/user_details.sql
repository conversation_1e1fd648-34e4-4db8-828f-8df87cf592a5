SELECT user.`user.main_email_address` AS `user_main_email_address`
            , user.password, user.user_id, profile.preferred_lang_code AS `profile_preferred_lang_code`, user.first_name, user.last_name, profile.profile_id as profile_id
            , user.brand_id, subscription_user.subscription_id, subscription_ml.title AS `subscription_name`
            , user.stars AS `user_stars`
            , profile.stars `profile_stars`
        FROM user.user_view as user
        LEFT JOIN profile_user.profile_user_view AS profile_user ON profile_user.user_id = user.user_id
        LEFT JOIN profile.profile_view AS profile ON profile.profile_id = profile_user.profile_id
        LEFT JOIN subscription_user.subscription_user_view AS subscription_user ON subscription_user.user_id = user.user_id
        LEFT JOIN subscription.subscription_ml_table AS subscription_ml ON subscription_ml.subscription_id = subscription_user.subscription_id AND subscription_ml.lang_code = 'en'
        WHERE user.`user.main_email_address` = '<EMAIL>' OR user.username = LOWER('<EMAIL>');