# This file should be in the future instead of setup.py
# https://python-poetry.org/docs/pyproject

# It seems we need to copy this file also to serverless-com repo, as required by dialog-workflow-python-package

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.poetry]
# TODO: Please update the name, similar to storage-local (suffix -local)
name = "message-local"
# I believe we are still using the version from setup.py and not from here until potery will work
version = "0.0.60" # https://pypi.org/project/message-local
description = "message-local Python Package"
readme = "README.md"
authors = [
    "Circlez.ai <<EMAIL>>",
]

# dephell deps convert --from pyproject.toml --from-format poetry --to setup.py --to-format setuppy
[tool.dephell.main]
from = { format = "poetry", path = "pyproject.toml" }
to = { format = "setuppy", path = "setup.py" }
