{
  "version": "0.2.0",
  "configurations": [
    
    {
      "name": "Python: Current File",
      "type": "debugpy",
      "request": "launch",
      "program": "${file}",
      "console": "internalConsole",
      "cwd": "${workspaceFolder}/message-local-python-package/message_local",
      "env": {
        "PYTHONPATH": "${workspaceFolder}/message-local-python-package/message_local"
      },
      "justMyCode": false,
    },
    {
      "name": "Python: Pytest",
      "type": "debugpy",
      "request": "launch",
      "module": "pytest",
      "args": [
        "-v",
        "${workspaceFolder}/message-local-python-package/message_local"
      ],
      "console": "integratedTerminal",
      "cwd": "${workspaceFolder}/message-local-python-package/message_local",
      "justMyCode": false
    }
  ]
}