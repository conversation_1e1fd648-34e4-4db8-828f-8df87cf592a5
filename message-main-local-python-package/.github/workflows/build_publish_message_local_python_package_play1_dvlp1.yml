name: Build Publish message-local-python-package to PyPI.org play1, dvlp1
on:
  push:
    branches: [ "BU-*", dev ]
    # pull_request:
    # branches:
    #  - dev

jobs:
  # message-local run
  publish-message-local-python-package-play1:
    name: MessageLocalP()Play1 Build, Test & Publish
    # TODO Can we add this here or better in publish_python_package?
    # if: github.event.pull_request.draft == false
    if: startsWith(github.ref, 'refs/heads/bu-')
    strategy:
      matrix:
        target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/publish_python_package.yml@main
    secrets: inherit
    with:
      environment-name: ${{ matrix.target-environments }}
      repo-name: message-main-local-python-package
      repo-directory: message-local-python-package
      #TODO Only for debugging, should be commented
      #logger-minimum-severity: 1

  # messages-local run

  # Run tests of other packages that are using this package (database-mysql-local-python) i.e. messages-local-python-package

  # Branch already merged
  run-messages-local-python-package-play1-dev:
    name: MessagesLocalP(dev)Play1 Run
    needs: publish-message-local-python-package-play1
    #if: startsWith(github.ref, 'refs/heads/bu-')
    if: false
    strategy:
      fail-fast: false
      matrix:
        # TODO We didn't manage to have multiple environments
        target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      repo-name: messages-local-python-package
      branch-name: dev
      repo-directory: messages-local-python-package
      package-directory: messages_local


  run-messages-local-python-package-play1-xxxx:
    name: MessagesLocalP(xxxx)Play1 Run
    needs: publish-message-local-python-package-play1
    #if: ${{ contains(github.event.head_commit.message, '[sanity]') }}
    if: false
    strategy:
      fail-fast: false
      matrix:
        # TODO We didn't manage to have multiple environments
        target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      environment-name: ${{ matrix.target_environments }}
      repo-name: messages-local-python-package
      # TODO When changing, please also change the "name:"
      branch-name: BU-xxxx
      repo-directory: messages-local-python-package
      package-directory: messages_local


  # message-send

  # Run tests of other packages that are using this package (database-mysql-local-python) i.e., MessageSend-local-python-package
  run-message-send-local-python-package-play1-dev:
    name: MessageSendLocalP(dev)Play1 Run
    if: startsWith(github.ref, 'refs/heads/bu-')
    needs: run-messages-local-python-package-play1-dev
    strategy:
      fail-fast: false
      matrix:
        # TODO We didn't manage to have multiple environments
        target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      environment-name: ${{ matrix.target_environments }}
      repo-name: message-send-local-python-package
      branch-name: dev
      repo-directory: message-send-local-python-package
      package-directory: message_send_local


  run-message-send-local-python-package-feature-play1-xxxx:
    name: MessageSendLocalP(xxxx)Play1 Run
    # if: startsWith(github.ref, 'refs/heads/bu-')
    # TODO: At this point, there is no feature branch in this repo
    if: false
    strategy:
      fail-fast: false
      #matrix:
      ## TODO We didn't manage to have multiple environments
      #target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    needs: run-messages-local-python-package-play1-dev
    secrets: inherit
    with:
      repo-name: message-send-local-python-package
      branch-name: BU-xxxx--Adjust-classes-to-support-the-latest-version-of-GenericCrudMl--Gil-Azani
      repo-directory: message-send-platform-invitation-local-python_package
      package-directory: message_send_platform_invitation


  # TODO Need to add here all the packages/Classes that inherit message-local (SMS Inforu, email, WhatsApp, Facebook, Selenium ...)


  run-facebook-message-selenium-local-python-package-play1:
    name: FBMsgSelLocalP()Play1 Run
    # TODO When using this "if: startsWith(github.ref, 'refs/heads/bu-')", unfortunately, we can't use "on: pull_request:" - possible solutions 1. change the if 2. split GHA per environment
    if: startsWith(github.ref, 'refs/heads/bu-')
    #if: '! github.event.pull_request.draft'
    needs: publish-message-local-python-package-play1
    strategy:
      # This should be commented/true, when it is equal to false, the job can be Green, but in Summary, not Green
      #fail-fast: false
      matrix:
        target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    permissions:
      id-token: write      # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write      # To allow the Reusable GitHub Action (i.e.  For publish_python_package.yml) to push the file created/updated by Sql2Code
      pull-requests: write # To enable the Reusable GitHub Action (i.e.  For publish_python_package.yml) to create a Pull Request (PR) following Sql2Code
    secrets: inherit
    with:
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: facebook-message-selenium-local-python-package
      # Not needed for publish_ needed for run_
      #branch-name: dev
      repo-directory: facebook-message-selenium-local-python-package
      # Not needed for publish_ needed for run_
      #package-directory: <package-directory>
      #TODO Only for debugging can be changed to 1, debug, info ...
      #logger-minimum-severity: error
      #LOGGER_CONFIGURATION_JSON_PATH='contact-group-local-python-package\\.vscode\\logger_configuration.json'
      #TODO Only for debugging can be changed temporarily to true
      #logger-is-write-to-sql: false
      is-run-local: true
      #is-run-remote: true


  run-whatsapp-message-vonage-local-python-package-play1:
    name: WAMsgVonageLocP()Play1 Run
    if: startsWith(github.ref, 'refs/heads/bu-')
    needs: publish-message-local-python-package-play1
    strategy:
      fail-fast: false
      #matrix:
        #target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      #brand-name: Circlez
      #environment-name: ${{ matrix.target-environments }}
      repo-name: whatsapp-message-vonage-local-python-package
      # Not needed for publish_ needed for run_
      #branch-name: dev
      repo-directory: whatsapp_message_local_python_package
      # Not needed for publish_ needed for run_
      #package-directory: <package-directory>


# dvlp1
  publish-message-local-python-package-dvlp1-dev:
    name: MessageLocalP(dev)Dvlp1 Build, Test & Publish
    if: github.ref == 'refs/heads/dev'
    strategy:
      matrix:
        target_environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/publish_python_package.yml@main
    secrets: inherit
    with:
      environment-name: ${{ matrix.target_environments }}
      repo-name: message-local-python-package
      repo-directory: message-local-python-package
      #TODO Only for debugging, should be commented
      #logger-minimum-severity: 1


  run-messages-local-python-package-dvlp1-dev:
    name: MessagesLocalP(dev)Dvlp1 Run
    ## if: ${{ false }}
    if: github.ref == 'refs/heads/dev'
    needs: publish-message-local-python-package-dvlp1-dev
    strategy:
      fail-fast: false
      matrix:
        # TODO We didn't manage to have multiple environments
        target_environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      environment-name: ${{ matrix.target_environments }}
      repo-name: messages-local-python-package
      branch-name: dev
      repo-directory: messages-local-python-package
      package-directory: messages_local


  # Run tests of other packages that are using this package (database-mysql-local-python) i.e. MessageSend-local-python-package
  run-message-send-local-python-package-dvlp1-dev:
    name: MessageSendLocalP(dev)Dvlp1 Run
    if: github.ref == 'refs/heads/dev'
    needs: run-messages-local-python-package-dvlp1-dev
    strategy:
      fail-fast: false
      matrix:
        # TODO We didn't manage to have multiple environments
        target_environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      environment-name: ${{ matrix.target_environments }}
      repo-name: message-send-local-python-package
      branch-name: dev
      repo-directory: message-send-local-python-package
      package-directory: message_send_local
