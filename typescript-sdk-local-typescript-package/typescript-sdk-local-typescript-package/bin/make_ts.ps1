# To run in the 1st time
# /repos/circlez/typescript-sdk-local-typescript-package/typescript-sdk-local-typescript-package/bin/make_ts.ps1 
# D:\repos\circles\typescript-sdk-local-typescript-package\typescript-sdk-local-typescript-package\bin\make.ps1  

# TODO make_ts is in sdk-local and make_py is in sdk-remote, both need to be either in remote or local

function Install-And-Udate-Serverless-com {
    # TODO Install Serverless.com only if needed
    # Install Serverless v4 (XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX)
    npm i serverless -g

    # SERVERLESS_FRAMEWORK_FORCE_UPDATE=true
    $Env:SERVERLESS_FRAMEWORK_FORCE_UPDATE = "true"

    serverless update
} # Install-And-Udate-Serverless-com


# We better clean so it will be easier to see the erros of the current t
Clear-Host

Write-Host "make_ts.ps1 (From typescript-sdk-local)"

# This command should be in in D:\Users\tal\OneDrive\Documents\PowerShell\Microsoft.PowerShell_profile.ps1
# This command should be in in C:\Users\<USER>\OneDrive\Documents\PowerShell\Microsoft.PowerShell_profile.ps1
# Remove-Alias -Force -Name make_ts -ErrorAction SilentlyContinue
if ($IsLinux) {
    # Doesn't update the alias when calling the ps1, you should setup manually using alias make_ts="powershell /repos/circlez/typescript-sdk-local-typescript-package/typescript-sdk-local-typescript-package/bin/make_ts.ps1"
    #Write-Host IsLinux Set-Alias
    #Set-Alias -Force -Name make_ts -Value "/repos/circlez/typescript-sdk-local-typescript-package/typescript-sdk-local-typescript-package/bin/make_ts.ps1"
} elseif ($IsWindows) {
    # Should present it only if alias not exists
    Write-Host IsWindows Set-Alias make_ts
    Set-Alias --Force Name make_ts -Value D:\repos\circles\typescript-sdk-local-typescript-package\typescript-sdk-local-typescript-package\bin\make_ts.ps1
} else {
    Write-Host "Unknow OS"
}
#Write-Host "Alias is "
# Doesn't work if the alias do not exits
#Get-Alias -Name make_ts

# TODO move this to developers-sdk-remote repo relevant for TypeScript, Python, Php ...
#[ValidateCount(0, 3)]
#Param(
#    [string] $EnvironmentName,
#    [string] $CloudName
#)
###

if(Test-path "package.json" -PathType leaf)
 {
    # if true do something
    Write-Host "You are in the right directory (package.json exists)."
 }
else
{
    # if false do something
    Write-Host "You are not in the right directory (package.json is missing)."
    exit
}

# Changing to the project root
# $Directory1 = (Split-Path -Path (Get-Location) -Leaf)
# Write-Host $Directory1
# Write-Host (Get-Childitem . -include logger-remote-typescript-package -depth 2).FullName
# Set-Location (Get-Childitem . -include $Directory1 -depth 2).FullName

$ErrorOccured = $false

# Defaults
# "There is no =="
if ( $Args.Count -eq 0 ) {
    Write-Host "No parameters, doing all default steps. You can do only build, only lint or only test."
    # When true is does't look good in the terminal
    # When true is is safer but takes more time
    $isDeleteNodeModules=$true
    $build=$true
    $buildType=$install
    $lint=$true
    $test=$true
    $run=$true
}
else {
    Write-Host "number of arguments are " $args.Count
    Write-Host "Arguments are"$args
    ForEach ($arg in $args){
        write-output $arg
        if ($arg -eq "clean") {
            $isDeleteNodeModules=$true
        }
        if ($arg -eq "build") {
            $build=$true
        }
        if ($arg -eq "lint" -or $arg -eq "eslint" ) {
            $lint=$true
        }
        if ($arg -eq "test") {
            $test=$true
        }
        if ($arg -eq "full") {
            $buildType="update"
            $build=$true
        }
        if ($arg -eq "run") {
            $run=$true
        }
    }
}

# if ($PSBoundParameters.ContainsKey('lint')) {
#     $lint=$true
#     Write-Host "Lint requested"
# }

if ($build) {
    try {
        if ($isDeleteNodeModules) {
            Write-Host "Deleting node_modules"
            Remove-Item -Recurse -Force node_modules
            Write-Host "Finish deleting node_modules"
        }

        # Upgrade the npm
        # TODO Add condition
        #npm install npm -g

        $ErrorActionPreference = 'Stop'
        Write-Host "------------------ Install dependecies using 'npm update' (mandatory for build) ------------------"
        Switch ($buildType) {
            "update" { npm update } # Takes allot of time
            "install" { npm i }
            defaut { }
        }
        
        # To resolve case we are not using the right tsc command
        npm install typescript

        Write-Host "------------------ Build using 'npm run build' ------------------"
        If ($buildType -eq "update") {
            npm run build_full
        }
        Else {
            npm run build
        }
        If ($?)
        {
            "No errors in build"
        }
        Else
        {
            "Error in build"
            $ErrorOccured=$true
            exit
        }
    }
    catch
    {
        Write-Output "3"
        "Error occured"
        $ErrorOccured=$true
    }

    if($ErrorOccured) {
        Write-Host "Build failed, fix the code and run build.ps1 again"
        exit
    }

    if(!$ErrorOccured) {"No Error Occured during the build- Not accurate"}


}
if ($lint) {
    Write-Host "------------------ Lint using 'npm run lint' ------------------"
    npm run lint
    If ($?)
    {
        "No error"
    }
    Else
    {
        "Error in Lint"
        Write-Host $?
        $ErrorOccured=$true
        exit
    }
}

if ($test) {
    $file=".env.play1"
    if (Test-Path $file) {
        #$item=Get-Item $file
        Write-Host "The .env.play1 file exists. Details:"
        #$item
    } else {
        Write-Host "The .env.play1 file does not exist."
        Copy-Item "/repos/circlez/tal-circlez.ai/.env.play1" -Destination "."
        $env:NODE_ENV="play1"
        Write-Host "The .env.play1 copied to the current directory"
    }
}

if ($test) {
    Write-Host "------------------ Test using 'npm run test' ------------------"
    #doenv -v .env.play1 npm run test
    npm run test
    If ($?)
    {
        "No error in test"
    }
    Else
    {
        "Error in `npm run test`, you should run each test individually from VSCode with last `Debug` option and debug each test individually"
        $ErrorOccured=$true
        exit
    }
}

if ($run) {
    $currentDir = Get-Location
    $dirName = $currentDir.Name
    Write-Host "$currentDir="+ $currentDir
    Write-Host "$dirName="+ $dirName

    #if ($dirName -match "serverless-com" -and $dirName -match $word2) {
    if ($currentDir -match "serverless-com") {
        Write-Host "Directory name contains serverless-com"
        $serverlessDotCom=$true
    }

    if ($serverlessDotCom) {
        Write-Host "------------------ Serverless offline start ------------------"

        if (Get-Command serverless -ErrorAction SilentlyContinue) {
            Write-Host "Serverless is installed. Version:"
            serverless --version
        } else {
            Write-Host "Serverless is NOT installed."
            Install-And-Udate-Serverless-com
        }

        # TODO How can we run serverless with node --trace-deprecation?
        Write-Host "------------------ Serverless offline start ------------------"
        serverless offline start --stage local

        Write-Host "Please update the calling microservice component.json with the port"
    }
}


