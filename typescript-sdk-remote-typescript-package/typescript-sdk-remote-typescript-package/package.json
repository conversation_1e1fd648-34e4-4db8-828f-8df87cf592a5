{"name": "@circles-zone/typescript-sdk-remote", "version_comment": "https://github.com/circles-zone/typescript-sdk-remote-typescript-package/pkgs/npm/typescript-sdk-remote", "version": "0.0.108", "type_comment": "module was added in 0.0.83 and caused basic-reactjs-jsx-frontend to stop working. We should have the right jest.config.js syntax.", "description": "This is our TypeScript Software Development Kit (SDK) Local Package with common functions used by our TypeScript code", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["/dist", "/src"], "scripts": {"huskyInstall": "husky install", "prepare_comment": "The run all and npm-s workaround is so we can run the scripts without using && which only works on Linux", "prepare": "cd .. && husky typescript-sdk-remote-typescript-package/.husky", "upgrade_packages": "npm update --save", "tsc": "tsc", "compile": "npx tsc", "prebuild_comment": "TODO Should it be `prebuild` or `preinstall` https://stackoverflow.com/questions/41123631/how-to-get-the-version-from-the-package-json-in-typescript", "prebuild": "node -p \"'//export const PACKAGE_VERSION = ' + JSON.stringify(require('./package.json').version) + ';'\" > typescript-sdk-remote/src/version.ts", "build_comment": "We recommend doing build and not only compile, npm update takes allot of time, so do npm i on regular basis and npm update only after you pushed Green version", "build": "npm i && npx tsc", "build_full": "npm update && npx tsc", "lint_comment": "TODO We must have .eslintrc.cjs file", "lint_old": "eslint **/src/*.ts **/tests/*.ts --report-unused-disable-directives --max-warnings 0", "lint": "eslint **/src/**/*.ts **/tests/**/*.ts --ignore-pattern **/dist/**/*.ts --no-warn-ignored --report-unused-disable-directives --max-warnings 0 --fix", "test": "jest --config ./jest.config.js --forceExit --detectOpenHandles --coverage", "dev_comment": "TODO Please rename MarketplaceGoodsBackend to your directory", "dev": "nodemon --watch 'MarketplaceGoodsBackend/Src/*.ts --exec ts-node src/index.ts"}, "keywords": [], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@typescript-eslint/parser": "^8.17.0", "axios": "^1.7.3", "dotenv": "^16.3.1", "eslint": "^9.16.0", "http-status-codes": "^2.3.0", "middy-middleware-jwt-auth": "^6.0.0", "ts-node": "^10.9.1", "typescript": "^5.5.4"}, "devDependencies": {"@jest/globals": "^29.6.2", "@types/aws-lambda": "^8.10.116", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^8.17.0", "husky": "^9.1.4", "jest": "^29.5.0", "npm-run-all": "^4.1.5", "ts-jest": "^29.1.0", "typescript-eslint": "^8.17.0"}}