// TODO: As this is relevant not only for logger, let's move those to python-sdk repo.

// TODO I think we shoud move the usage of os.networkInterfaces to type-script-sdk-local as we get errors in React.js Application console
//import { networkInterfaces } from 'os';
/**
 * get the machine's IPv4 and IPv6 address
 * This function retrieves the network interfaces of the machine and iterates through them
 * to find the first non-internal IPv4 address. If found, it returns the address; otherwise, it returns undefined.
 */


export function getMachineIPv4(): string | undefined {
  // TODO change to console.warn()
  // TODO PACKAGE_VERSION="+ PACKAGE_VERSION
  console.log("As both Node.js and React.js is using Logger which is using getMachineIPv4 and network not working in React.js we return always ******* ");
  // We can't use os in React.js i.e basic-reactjs-frontend repo
  //console.log("getMachineIPv4");
  return "*******";
  /*
  const nets = networkInterfaces();
  for (const name of Object.keys(nets)) {
    for (const net of nets[name]!) {
      if (net.family === 'IPv4' && !net.internal) {
        return net.address;
      }
    }
  }
  return undefined; // Return undefined if no valid IPv4 address found
  */
}

export function getMachineIPv6(): string | undefined {
  // We can't use os in React.js i.e basic-reactjs-frontend repo
  //console.log("getMachineIPv6");
  return "2001:0000:130F:0000:0000:09C0:876A:130B";
  /*
  const nets = networkInterfaces();
  for (const name of Object.keys(nets)) {
    for (const net of nets[name]!) {
      if (net.family === 'IPv6' && !net.internal) {
        return net.address;
      }
    }
  }
  return undefined; // Return undefined if no valid IPv6 address found
  */
}

// This function uses a regular expression to validate whether a given string is a valid IPv4 address.
// The regular expression checks for four groups of one to three digits separated by dots, with each group ranging from 0 to 255.
export function validateIPv4(ip: string): boolean {
  const ipv4Regex = /^(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]{1,2})(\.(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]{1,2})){3}$/;
  return ipv4Regex.test(ip);
}


// This function uses a regular expression to validate whether a given string is a valid IPv6 address.
// The regular expression checks for eight groups of one to four hexadecimal digits separated by colons.
export function validateIPv6(ip: string): boolean {
  const ipv6Regex = /^(([0-9a-fA-F]{1,4}:){7}([0-9a-fA-F]{1,4}|:)|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}(:[0-9a-fA-F]{1,4}){1,6}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,5}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1}(:[0-9a-fA-F]{1,4}):|(:([0-9a-fA-F]{1,4})){1,7})$/;
  const result = ipv6Regex.test(ip);
  return result;
}
