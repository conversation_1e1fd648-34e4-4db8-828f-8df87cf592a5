CREATE VIEW campaign.campaign_criteria_set_criteria_profile_general_view AS
SELECT campaign_criteria.campaign_id, campaign_criteria.criteria_set_id, criteria_set_profile.profile_id AS `criteria_set_profile.profile_id`
, campaign_criteria.criteria_id, criteria_profile.profile_id as `criteria_profile.profile_id`
FROM campaign.campaign_criteria_set_criteria_general_view AS campaign_criteria
-- JOIN criteria.criteria_set_general_view AS criteria_set_general ON criteria_set_general.child_criteria_set_id=campaign_criteria.criteria_set_id
LEFT OUTER JOIN criteria_profile.criteria_profile_general_view AS criteria_profile
    ON criteria_profile.criteria_id=campaign_criteria.criteria_id
LEFT OUTER JOIN criteria_profile.criteria_set_profile_view AS criteria_set_profile
 	ON criteria_set_profile.criteria_set_id=campaign_criteria.criteria_set_id
-- WHERE campaign_id=1;
;
