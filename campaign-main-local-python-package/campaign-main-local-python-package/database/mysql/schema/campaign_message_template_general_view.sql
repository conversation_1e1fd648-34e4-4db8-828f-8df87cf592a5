CREATE 
    ALGORITHM = UNDEFINED 
    DEFINER = `bubbelz`@`%` 
    SQL SECURITY DEFINER
VIEW `campaign_message_template`.`campaign_message_template_general_view` AS
    SELECT 
        `campaign`.`campaign_general`.`campaign_id` AS `campaign_id`,
        `campaign_message_template`.`percent` AS `campaign_message_template.percent`,
        `message_template`.`message_template_general`.`message_template_id` AS `message_template_id`,
        `message_template`.`message_template_general`.`message_template.name` AS `message_template.name`,
        `message_template`.`message_template_general`.`message_template_text_block_seq` AS `message_template_text_block_seq`,
        `message_template`.`message_template_general`.`message_template_text_block_name` AS `message_template_text_block_name`,
        `message_template`.`message_template_general`.`email_subject_template` AS `email_subject_template`,
        `message_template`.`message_template_general`.`email_body_html_template` AS `email_body_html_template`,
        `message_template`.`message_template_general`.`sms_body_template` AS `sms_body_template`,
        `message_template`.`message_template_general`.`whatsapp_body_template` AS `whatsapp_body_template`,
        `message_template`.`message_template_general`.`message_template_text_block_type.title` AS `message_template_text_block_type.title`,
        `message_template`.`message_template_general`.`question_id` AS `question_id`,
        `question`.`question_general`.`question.name` AS `question.name`,
        `question`.`question_general`.`title` AS `question.title`
    FROM
        (((`campaign`.`campaign_general_view` `campaign_general`
        LEFT JOIN `campaign_message_template`.`campaign_message_template_table` `campaign_message_template` ON ((`campaign_message_template`.`campaign_id` = `campaign`.`campaign_general`.`campaign_id`)))
        LEFT JOIN `message_template`.`message_template_general_view` `message_template_general` ON ((`message_template`.`message_template_general`.`message_template_id` = `campaign_message_template`.`message_template_id`)))
        LEFT JOIN `question`.`question_general_view` `question_general` ON ((`question`.`question_general`.`question_id` = `message_template`.`message_template_general`.`question_id`)))
    ORDER BY `message_template`.`message_template_general`.`message_template_id` , `message_template`.`message_template_general`.`message_template_text_block_seq`
