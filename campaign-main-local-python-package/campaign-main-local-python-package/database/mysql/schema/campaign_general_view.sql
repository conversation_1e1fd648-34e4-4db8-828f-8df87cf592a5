<<<<<<< HEAD
-- CREATE VIEW campaign.campaign_general_with_deteled_and_test_data_view AS
-- SELECT -- *
--     campaign.campaign_id
--      , campaign.name
-- -- ,campaign.message_template_id
--      , campaign.dialog_workflow_script_id
--      , criteria.criteria_id AS 'campaign.criteria.criteria_id'
--      , criteria.name        AS 'campaign.criteria.name'
--      , message_template_general.*
-- FROM campaign.campaign_table AS campaign
--          LEFT JOIN message_template.message_template_general_view AS message_template_general
--                    ON message_template_general.message_template_id = campaign.message_template_id
--          LEFT JOIN campaign_criteria.campaign_criteria_table AS campign_criteria
--                    ON campign_criteria.campaign_id = campaign.campaign_id
--          LEFT JOIN criteria.criteria_table AS criteria ON criteria.criteria_id = campign_criteria.criteria_id
-- ;


CREATE 
VIEW `campaign`.`campaign_general_with_deleted_and_test_data_view` AS
    SELECT 
        `campaign`.`campaign_id` AS `campaign_id`,
        `campaign`.`name` AS `name`,
        `campaign`.`criteria_id_old` AS `criteria_id_old`,
        `campaign`.`start_hour` AS `start_hour`,
        `campaign`.`end_hour` AS `end_hour`,
        `campaign`.`occurrence_id` AS `occurrence_id`,
        `campaign`.`days_of_week` AS `days_of_week`,
        `campaign`.`max_audience` AS `max_audience`,
        `campaign`.`max_exposure_per_day` AS `max_exposure_per_day`,
        `campaign`.`minimal_days_between_messages_to_the_same_recipient` AS `minimal_days_between_messages_to_the_same_recipient`,
        `campaign`.`message_template_id_old` AS `message_template_id_old`,
        `campaign`.`dialog_workflow_script_id` AS `dialog_workflow_script_id`,
        `campaign`.`default_sender_profile_id` AS `default_sender_profile_id`,
        `campaign`.`is_dialog_workflow` AS `is_dialog_workflow`,
        `campaign`.`is_test_data` AS `is_test_data`,
        `campaign`.`start_timestamp` AS `start_timestamp`,
        `campaign`.`end_timestamp` AS `end_timestamp`,
        `campaign`.`created_timestamp` AS `created_timestamp`,
        `campaign`.`created_user_id` AS `created_user_id`,
        `campaign`.`updated_timestamp` AS `updated_timestamp`,
        `campaign`.`updated_user_id` AS `updated_user_id`,
        `dialog_workflow_script`.`start_state_id` AS `start_state_id`
    FROM
        (`campaign`.`campaign_table` `campaign`
        LEFT JOIN `dialog_workflow`.`dialog_workflow_script_table` `dialog_workflow_script` ON ((`dialog_workflow_script`.`dialog_workflow_script_id` = `campaign`.`dialog_workflow_script_id`)))
=======
CREATE VIEW `campaign`.`campaign_general_view` AS
SELECT * FROM campaign.campaign_general_with_deleted_and_test_data_view
WHERE is_test_data IS FALSE;
>>>>>>> f4c81a2d53c765a861c8849a94a470e829f13734
