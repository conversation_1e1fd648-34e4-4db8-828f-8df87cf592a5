CREATE VIEW campaign.campaign_general_with_deleted_and_test_data_view AS
SELECT -- *
    campaign.campaign_id
     , campaign.name
-- ,campaign.message_template_id
     , campaign.dialog_workflow_script_id
     , criteria.criteria_id AS 'campaign.criteria.criteria_id'
     , criteria.name        AS 'campaign.criteria.name'
     , message_template_general.*
FROM campaign.campaign_table AS campaign
         LEFT JOIN message_template.message_template_general_view AS message_template_general
                   ON message_template_general.message_template_id = campaign.message_template_id
         LEFT JOIN campaign_criteria.campaign_criteria_table AS campign_criteria
                   ON campign_criteria.campaign_id = campaign.campaign_id
         LEFT JOIN criteria.criteria_table AS criteria ON criteria.criteria_id = campign_criteria.criteria_id
;
