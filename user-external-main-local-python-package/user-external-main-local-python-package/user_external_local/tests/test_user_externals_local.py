# When testing using .env file, uncomment below
# from logger_local.MetaLogger import module_wrapper

from dotenv import load_dotenv
from datetime import datetime
from time import sleep
from logger_local.LoggerComponentEnum import LoggerComponentEnum
from logger_local.LoggerLocal import Logger
import mysql.connector
import time


from src.user_externals_local import (
    UserExternalsLocal,
    USER_EXTERNAL_LOCAL_PYTHON_PACKAGE_COMPONENT_ID,
    USER_EXTERNAL_LOCAL_PYTHON_PACKAGE_COMPONENT_NAME,
    DEVELOPER_EMAIL,
)

load_dotenv()

# not used we now generate a user for each test, but kept for reference
# TEST_USER_EXTERNAL = "TEST" + str(datetime.now())
# TEST_STATIC_USER_EXTERNAL = "TEST_STATIC" + str(datetime.now())

USER_EXTERNAL_TEST_LOGGER_OBJECT = {
    "component_id": USER_EXTERNAL_LOCAL_PYTHON_PACKAGE_COMPONENT_ID,
    "component_name": USER_EXTERNAL_LOCAL_PYTHON_PACKAGE_COMPONENT_NAME,
    "component_category": LoggerComponentEnum.ComponentCategory.Unit_Test.value,
    "developer_email": DEVELOPER_EMAIL,
    "testing_framework": LoggerComponentEnum.testingFramework.pytest.value,
}

logger = Logger.create_logger(object=USER_EXTERNAL_TEST_LOGGER_OBJECT)

# TODO if it's possible to use only 1 TEST_PROFILE_ID, use get_test_profile_id method to get it
# https://github.com/circles-zone/profile-main-local-python-package/blob/d301308fc5e1ffb00ec3333c7803b556636b3cd5/profile_local_python_package/profile_local/src/profiles_local.py#L112C9-L112C28  # noqa
# TODO It is not recommended to use Magic Numbers in the code, it will not work on a brand-new database or another environment, and we can't use existing data which is not is_test_data  # noqa

TEST_PROFILE_ID1 = 5000002
TEST_PROFILE_ID2 = 50000392
TEST_PROFILE_ID3 = 50000409
TEST_PROFILE_ID4 = 50000405
# TODO SystemsLocals.get_test_system_id() like we do in places like ContactsLocal.get_test_contact_id()
TEST_SYSTEM_ID = 1


user_externals_local = UserExternalsLocal(is_test_data=True)


def test_insert_get():
    # TODO test_user_external = UserExternalsLocal.get_test_user_external(__name__) like we should have  ContactsLocal.get_test_contact(__name__)
    test_username = "test_username test_insert_get " + str(datetime.now())
    access_token_test = (
        "access_token_test from test_insert_get() of test_user_externals_local.py"
    )

    user_externals_local.insert_or_update_user_external_access_token(
        username=test_username,
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
        access_token=access_token_test,
    )
    token = user_externals_local.get_access_token(
        username=test_username,
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
    )
    assert token == access_token_test


def test_update_access_token():
    test_username = "test_username test_update_access_token " + str(datetime.now())
    inseted_access_token_test = "inseted_access_token_test from test_update_access_token() of test_user_externals_local.py"

    inserted_id = user_externals_local.insert_or_update_user_external_access_token(
        system_id=TEST_SYSTEM_ID,
        username=test_username,
        access_token=inseted_access_token_test,
        profile_id=TEST_PROFILE_ID1,
    )

    updated_access_token_test = "updated_access_token_test from test_update_access_token() of test_user_externals_local.py"
    updated_id = user_externals_local.update_user_external_access_token(
        user_external_id=inserted_id,
        username=test_username,
        system_id=TEST_SYSTEM_ID,
        profile_id=TEST_PROFILE_ID1,
        access_token=updated_access_token_test,
    )

    token = user_externals_local.get_access_token(
        user_external_id=updated_id,
        username=test_username,
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
    )
    assert token == updated_access_token_test


def test_delete_access_token():
    test_username = "test_username test_delete_access_token " + str(datetime.now())

    user_externals_local.delete_access_token(
        username=test_username,
        system_id=TEST_SYSTEM_ID,
        profile_id=TEST_PROFILE_ID1,
    )

    sleep(2)

    token = user_externals_local.get_access_token(
        username=test_username,
        system_id=TEST_SYSTEM_ID,
        profile_id=TEST_PROFILE_ID1,
    )
    assert token is None


def test_insert_by_system_and_profile_get():
    # TODO test_user_external = UserExternalsLocal.get_test_user_external(__name__)
    test_username = "test_username test_insert_by_system_and_profile_get " + str(
        datetime.now()
    )

    access_token_test = "access_token_test test_insert_by_system_and_profile_get"
    refresh_token_test = "refresh_test test_insert_by_system_..."
    expiry_test = "expiry_test test_insert_by_system_..."

    # TODO the order of the parameters should be profile_id, system_id, username, refresh_token ...
    user_externals_local.insert_or_update_user_external_access_token(
        username=test_username,
        profile_id=TEST_PROFILE_ID2,
        system_id=TEST_SYSTEM_ID,
        access_token=access_token_test,
        expiry=expiry_test,
        refresh_token=refresh_token_test,
    )

    auth_details = user_externals_local.get_auth_details_by_system_id_and_profile_id(
        profile_id=TEST_PROFILE_ID2, system_id=TEST_SYSTEM_ID
    )

    assert auth_details is not None
    assert auth_details.get("access_token") == access_token_test
    assert auth_details.get("refresh_token") == refresh_token_test
    assert auth_details.get("expiry") == expiry_test


def test_get_access_token_by_username_and_system_id():
    test_username = (
        "test_username test_get_access_token_by_username_and_system_id "
        + str(datetime.now())
    )
    access_token_test = (
        "access_token_test test_get_access_token_by_username_and_system_id"
    )

    user_externals_local.insert_or_update_user_external_access_token(
        username=test_username,
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
        access_token=access_token_test,
    )

    token = user_externals_local.get_access_token_by_username_and_system_id(
        username=test_username, system_id=TEST_SYSTEM_ID
    )

    assert token == access_token_test


# Tests for the deprecated static methods
def test_insert_get_static():
    logger.start("test started")
    test_username = "test_username test_insert_get_static " + str(datetime.now())
    access_token_test = "access_token_test"
    user_externals_local.insert_or_update_user_external_access_token(
        username=test_username,
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
        access_token=access_token_test,
    )
    token = user_externals_local.get_access_token(
        username=test_username,
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
    )
    assert token == access_token_test
    logger.end("test successfull")


def test_update_access_token_static():
    logger.start("test started")
    test_username = "test_username test_update_access_token_static " + str(
        datetime.now()
    )
    new_access_token_test = "new_access_token_test"

    inserted_user_externals_id = (
        user_externals_local.insert_or_update_user_external_access_token(
            username=test_username,
            profile_id=TEST_PROFILE_ID1,
            system_id=TEST_SYSTEM_ID,
            access_token=new_access_token_test,
        )
    )

    user_externals_local.update_user_external_access_token(
        user_external_id=inserted_user_externals_id,
        username=test_username,
        system_id=TEST_SYSTEM_ID,
        profile_id=TEST_PROFILE_ID1,
        access_token=new_access_token_test,
    )

    token = user_externals_local.get_access_token(
        username=test_username,
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
    )

    assert token == new_access_token_test
    logger.end("test successfull")


def test_delete_access_token_static():
    logger.start("test started")
    test_username = "test_username test_delete_access_token_static " + str(
        datetime.now()
    )
    access_token_test = "access_token_test test_delete_access_token_static"

    user_externals_local.insert_or_update_user_external_access_token(
        username=test_username,
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
        access_token=access_token_test,
    )

    user_externals_local.delete_access_token(
        username=test_username,
        system_id=TEST_SYSTEM_ID,
        profile_id=TEST_PROFILE_ID1,
    )

    sleep(2)

    token = user_externals_local.get_access_token(
        username=test_username,
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
    )

    assert token is None
    logger.end("test successfull")


def test_insert_by_system_and_profile_get_static():
    logger.start("test started")

    test_username = "test_username test_insert_by_system_and_profile_get_static " + str(
        datetime.now()
    )

    access_token_test = "access_token_test test_insert_by_system_and_profile_get_static"
    expiry_test = "expiry_test test_insert_by_system_an..."
    refresh_token_test = "refresh_test test_insert_by_system_..."

    user_externals_local.insert_or_update_user_external_access_token(
        username=test_username,
        profile_id=TEST_PROFILE_ID2,
        system_id=TEST_SYSTEM_ID,
        access_token=access_token_test,
        expiry=expiry_test,
        refresh_token=refresh_token_test,
    )

    auth_details = user_externals_local.get_auth_details_by_system_id_and_profile_id(
        profile_id=TEST_PROFILE_ID2, system_id=TEST_SYSTEM_ID
    )

    assert auth_details is not None
    assert auth_details.get("access_token") == access_token_test
    assert auth_details.get("refresh_token") == refresh_token_test
    assert auth_details.get("expiry") == expiry_test

    logger.end("test successfull")


def test_get_access_token_by_username_and_system_id_static():
    logger.start()
    test_username = (
        "test_username test_get_access_token_by_username_and_system_id_static "
        + str(datetime.now())
    )
    access_token_test = (
        "access_token_test test_get_access_token_by_username_and_system_id_static"
    )

    user_externals_local.insert_or_update_user_external_access_token(
        username=test_username,
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
        access_token=access_token_test,
    )

    access_token = user_externals_local.get_access_token(
        username=test_username,
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
    )

    assert access_token == access_token_test
    logger.end("test successfull")


def test_get_credentials_storage_id_by_system_id_and_profile_id():
    test_credentials_storage_id = (
        user_externals_local.get_credential_storage_id_by_system_id_and_profile_id(
            profile_id=TEST_PROFILE_ID1, system_id=TEST_SYSTEM_ID
        )
    )
    correct_credential_storage_id = user_externals_local.select_one_value_by_where(
        schema_name=user_externals_local.default_schema_name,
        view_table_name=user_externals_local.default_view_table_name,
        select_clause_value="credential_storage_id",
        where=f"system_id={TEST_SYSTEM_ID} AND username={TEST_PROFILE_ID1}",
    )
    assert test_credentials_storage_id == correct_credential_storage_id
    logger.end()


def test_cannot_insert_two_identical_entries():
    data_to_insert = {
        # TODO Replace Magic Numbers such as 1 with enum/const from python-sdk-remote system.py
        "system_id":1,
        "main_profile_id":5000002,
        "username":"test_username_"+str(datetime.now()),
        "is_test_data":True,
    }
    user_externals_local.insert(
        data_dict=data_to_insert,
    )
    try:
        user_externals_local.insert(
            data_dict=data_to_insert,
        )
        assert False
    except mysql.connector.errors.IntegrityError:
        assert True
    

def test_get_password_clear_text_by_system_id_and_profile_id():
    test_username = (
        "test_username " + str(datetime.now())
    )
    access_token_test = (
        "access_token_test"
    )

    test_password = "test_password_clear_text"

    # uncomment and update the TEST_PROFILE_ID_4 if you want to insert a new entry
    
    # TODO How can we make it work in all databases / empty databases? Can we enhance ProfilesLocal.get_test_profile_id() so we can use it?

    # user_externals_local.insert_or_update_user_external_access_token(
    #     username=test_username,
    #     profile_id=TEST_PROFILE_ID3,
    #     system_id=TEST_SYSTEM_ID,
    #     access_token=access_token_test,
    # )
    # test_user_external_id = user_externals_local.get_user_external_id_by_profile_id_system_id_username(
    #     profile_id=TEST_PROFILE_ID3,
    #     system_id=TEST_SYSTEM_ID,
    #     username=test_username
    #     )

    # test_data = {"password_clear_text": test_password, "user_external_id": test_user_external_id}

    # user_externals_local.user_external_pii.insert(
    #     data_dict=test_data,
    #     commit_changes=True,
    # )
    # user_externals_local.connection.commit()

    assert test_password == user_externals_local.get_password_clear_text_by_system_id_and_profile_id(system_id=TEST_SYSTEM_ID, profile_id=TEST_PROFILE_ID3)

    try:
        user_externals_local.get_password_clear_text_by_system_id_and_profile_id(system_id=TEST_SYSTEM_ID, profile_id=TEST_PROFILE_ID4)
        assert False
    except mysql.connector.errors.InternalError:
        assert True

def test_get_password_clear_text_by_system_id_profile_id_username():
    test_username = (
        "test_username " + str(datetime.now())
    )
    access_token_test = (
        "access_token_test"
    )

    user_externals_local.insert_or_update_user_external_access_token(
        username=test_username,
        profile_id=TEST_PROFILE_ID2,
        system_id=TEST_SYSTEM_ID,
        access_token=access_token_test,
    )
    test_user_external_id = user_externals_local.get_user_external_id_by_profile_id_system_id_username(
        profile_id=TEST_PROFILE_ID2,
        system_id=TEST_SYSTEM_ID,
        username=test_username
        )
    
    test_password = "test_password_clear_text"

    test_data = {"password_clear_text": test_password, "user_external_id": test_user_external_id}

    user_externals_local.user_external_pii.insert(
        data_dict=test_data,
        commit_changes=True,
    )
    user_externals_local.connection.commit()

    assert test_password == user_externals_local.get_password_clear_text_by_system_id_profile_id_username(system_id=TEST_SYSTEM_ID, 
                                                                                                          profile_id=TEST_PROFILE_ID2, 
                                                                                                          username=test_username
                                                                                                          )





# to use to debugge the code, uncomment the test function you want to run
if __name__ == "__main__":
    # test_insert_get()
    test_update_access_token()
    # test_delete_access_token()
    # test_insert_by_system_and_profile_get()
    # test_get_access_token_by_username_and_system_id()
    # test_insert_get_static()
    # test_update_access_token_static()
    # test_delete_access_token_static()
    # test_insert_by_system_and_profile_get_static()
    # test_get_access_token_by_username_and_system_id_static()
    # test_get_credentials_storage_id_by_system_id_and_profile_id()
