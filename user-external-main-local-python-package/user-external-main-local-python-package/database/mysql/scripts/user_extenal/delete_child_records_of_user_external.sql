SELECT user_external_id, username, system_id, subsystem_id, main_profile_id, is_test_data, end_timestamp
FROM user_external.user_external_table
WHERE end_timestamp IS NOT NULL
GROUP BY username, system_id, subsystem_id, main_profile_id
HAVING count(*)>1;

-- works
DELETE FROM import_contact.import_contact_table_old
WHERE importer_table_id IN (SELECT importer_table_id
FROM user_external.user_external_table WHERE is_test_data=TRUE AND end_timestamp IS NOT NULL AND is_test_data=1);


-- works
DELETE FROM importer.importer_table
WHERE user_external_id IN (SELECT user_external_id
FROM user_external.user_external_table WHERE is_test_data=TRUE AND end_timestamp IS NOT NULL AND is_test_data=1);


-- 
-- DELETE FROM contact.contact_table
-- WHERE data_source_instance_id IN
-- ( 

SELECT data_source_instance_id FROM data_source_instance.data_source_instance_table
WHERE user_external_id IN 
( SELECT user_external_id
FROM user_external.user_external_table
WHERE end_timestamp IS NOT NULL
GROUP BY username, system_id, subsystem_id, main_profile_id
HAVING count(*)>1);

DELETE FROM data_source_instance.data_source_instance_table
WHERE user_external_id IN 
 -- (SELECT user_external_id FROM user_external.user_external_table WHERE is_test_data=TRUE AND end_timestamp IS NOT NULL);
 (SELECT user_external_id -- , username, system_id, subsystem_id, main_profile_id, is_test_data, end_timestamp
FROM user_external.user_external_table
WHERE end_timestamp IS NOT NULL
GROUP BY username, system_id, subsystem_id, main_profile_id
HAVING count(*)>1);

UPDATE data_source_instance.data_source_instance_table
SET user_external_id = 
(SELECT user_external_id FROM user_external.user_external_table
WHERE end_timestamp IS NULL
AND username=expired_user_external.username
AND main_profile_id=expired_user_external.main_profile_id
AND system_id=expired_user_external.system_id)
WHERE user_external_id IN 
(SELECT user_external_id -- , username, system_id, subsystem_id, main_profile_id, is_test_data, end_timestamp
FROM user_external.user_external_table
WHERE end_timestamp IS NOT NULL
GROUP BY username, system_id, subsystem_id, main_profile_id
HAVING count(*)>1) ;





-- Works
DELETE FROM profile_user_external.profile_user_external_table
WHERE user_external_id IN 
 -- (SELECT user_external_id FROM user_external.user_external_table WHERE is_test_data=TRUE AND end_timestamp IS NOT NULL);
 (SELECT user_external_id -- , username, system_id, subsystem_id, main_profile_id, is_test_data, end_timestamp
FROM user_external.user_external_table
WHERE end_timestamp IS NOT NULL
GROUP BY username, system_id, subsystem_id, main_profile_id
HAVING count(*)>1);

-- Works
DELETE FROM contact_user_external.contact_user_external_table
WHERE user_external_id IN 
 -- (SELECT user_external_id FROM user_external.user_external_table WHERE is_test_data=TRUE AND end_timestamp IS NOT NULL);
 (SELECT user_external_id -- , username, system_id, subsystem_id, main_profile_id, is_test_data, end_timestamp
FROM user_external.user_external_table
WHERE end_timestamp IS NOT NULL
GROUP BY username, system_id, subsystem_id, main_profile_id
HAVING count(*)>1);

-- Works
DELETE FROM api_call.api_call_table
WHERE user_external_id IN 
 -- (SELECT user_external_id FROM user_external.user_external_table WHERE is_test_data=TRUE AND end_timestamp IS NOT NULL);
 (SELECT user_external_id -- , username, system_id, subsystem_id, main_profile_id, is_test_data, end_timestamp
FROM user_external.user_external_table
WHERE end_timestamp IS NOT NULL
GROUP BY username, system_id, subsystem_id, main_profile_id
HAVING count(*)>1);

DELETE FROM user_external.user_external_table WHERE is_test_data=TRUE and end_timestamp IS NOT NULL AND is_test_data=TRUE;

SELECT user_external_id -- , username, system_id, subsystem_id, main_profile_id, is_test_data, end_timestamp
FROM user_external.user_external_table
WHERE end_timestamp IS NOT NULL
GROUP BY username, system_id, subsystem_id, main_profile_id
HAVING count(*)>1;