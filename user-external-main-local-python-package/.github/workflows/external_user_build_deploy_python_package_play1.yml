name: Build Publish External User Local Python Package to PyPI.org play1
on:
  push:
    branches: [ "BU-*" ]
  #pull_request:
   #branches: [dev]

env:
  brand_name: Circlez
  environment_name: play1
  repo_name: user-external-main-local-python-package

jobs:
  deploy:
    if: false
    runs-on: ubuntu-latest
    environment:
      name: play1 # If using $environment_name, GitHub Environment Secrets are not deployed in Lambda Function Environment
      url: https://${{ env.environment_name }}.circ.zone
    strategy:
      #fail-fast: false
      matrix:
        #python-version: ["3.12"] # We changed from python-version 3.x (which was 3.12) to 3.10 so we can use torch in requirements.txt in a Python Package
        python-version: [ '3.x' ] # Latest 3.x version (Currently 3.12)
        #poetry-version: ["2.1.6"]
    steps:
      - uses: actions/checkout@v4.1.0 # https://github.com/actions/checkout

      - name: Set up Python
        uses: actions/setup-python@v4.7.1 # https://github.com/actions/setup-python
        with:
          #python-version: "3.x"
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          cd ./$repo_name
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Lint with flake8
        run: |
          python -m pip install flake8 pytest
          # stop the build if there are Python syntax errors or undefined names
          flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          # exit-zero treats all errors as warnings. The GitHub editor is 127 characters wide
          flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics   

      - name: Build package
        run: |
          cd ./$repo_name
          pip install build
          python -m build

      - name: Run Tests
        run: pytest
        env:
          BRAND_NAME: ${{ env.brand_name }}
          ENVIRONMENT_NAME: ${{ env.environment_name }}

          LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', env.environment_name)] }}

          RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', env.environment_name)] }}
          RDS_USERNAME: ${{ vars.RDS_USERNAME }}
          RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}

          PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', env.environment_name)] }}
          PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', env.environment_name)] }}

      # TODO: Please create pytest-coverage.txt and pytest.xml
      - name: Pytest coverage comment
        uses: MishaKav/pytest-coverage-comment@main
        with:
          pytest-coverage-path: ./pytest-coverage.txt
          junitxml-path: ./pytest.xml

      # TODO: I've added a few more steps to the storage-local-python-package to support poetry. Do we need them?

      # TODO: Please make sure you have created pyproject.toml file and uncomment the below lines, and the version is patched every time we run the GHA
      - name: Patch the version number in pyproject.toml
        run: |
          pip install poetry
          cd ./$repo_name
          poetry version patch

      - name: Publish package
        uses: pypa/gh-action-pypi-publish@27b31702a0e7fc50959f5ad993c78deac1bdfc29
        with:
          user: __token__
          password: ${{ secrets.PYPI_API_TOKEN }}
          packages_dir: ${{ env.repo_name }}/dist


  # play1 Build, Test & Publish
  publish-user-external-main-local-python-package-play1:
    name: UserExtLocP()Play1 Build, Test & Publish
    # TODO When using this "if: startsWith(github.ref, 'refs/heads/bu-')", unfortunately, we can't use "on: pull_request:"
    if: startsWith(github.ref, 'refs/heads/bu-')
    strategy:
      fail-fast: false
      matrix:
        target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/publish_python_package.yml@main
    permissions:
      id-token: write # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write # To allow the Reusable GitHub Action (i.e., for publish_python_package.yml) to push the file created/updated by Sql2Code
      pull-requests: write # To enable the Reusable GitHub Action (i.e. for publish_python_package.yml) to create a Pull Request (PR) following Sql2Code
    secrets: inherit
    with:
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: user-external-main-local-python-package
      # Not needed for publish_ needed for run_
      #branch-name: dev
      repo-directory: user-external-main-local-python-package
      # Not needed for publish_ needed for run_
      #package-directory: <package-directory>
      #TODO Only for debugging can be changed to 1, debug, info ...
      #logger-minimum-severity: error
      #LOGGER_CONFIGURATION_JSON_PATH='contact-group-local-python-package\\.vscode\\logger_configuration.json'
      #TODO Only for debugging can be changed temporarily to true
      #logger-is-write-to-sql: false


  run-contact-person-profile-csv-imp-local-python-package-play1-dev:
    name: ContPerProCsvImpLocalP(dev)Play1 Run
    needs: publish-user-external-main-local-python-package-play1
    if: startsWith(github.ref, 'refs/heads/bu-')
    strategy:
      fail-fast: false
      matrix:
        target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      repo-name: contact-person-profile-csv-imp-local-python-package
      repo-directory: contact-person-profile-csv-imp-local


  # play1 Build, Test & Publish
  run-google-account-local-python-package-play1-dev:
    name: GoogleAccountLocP(dev)Play1 Build, Test & Publish
    needs: publish-user-external-main-local-python-package-play1
    # TODO When using this "if: startsWith(github.ref, 'refs/heads/bu-')", unfortunately, we can't use "on: pull_request:" - possible solutions 1. change the if 2. split GHA per environment
    if: startsWith(github.ref, 'refs/heads/bu-')
    #if: '! github.event.pull_request.draft'
    strategy:
      fail-fast: false
      matrix:
        target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    permissions:
      id-token: write      # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write      # To allow the Reusable GitHub Action (i.e., for publish_python_package.yml) to push the file created/updated by Sql2Code
      pull-requests: write # To enable the Reusable GitHub Action (i.e, for publish_python_package.yml) to create a Pull Request (PR) following Sql2Code
    secrets: inherit
    with:
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: google-account-local-python-package
      # Not needed for publish_ needed for run_
      #branch-name: dev
      repo-directory: google-account-local-python-package
      # Not needed for publish_ needed for run_
      #package-directory: <package-directory>
      #TODO Only for debugging can be changed to 1, debug, info ...
      #logger-minimum-severity: error
      #LOGGER_CONFIGURATION_JSON_PATH='contact-group-local-python-package\\.vscode\\logger_configuration.json'
      #TODO Only for debugging can be changed temporarily to true
      #logger-is-write-to-sql: false
      is-run-local: true
      #is-run-remote: true


  run-google-contact-local-python-package-play1-dev:
    name: GoogleContactLocP(dev)Play1 Run
    needs: publish-user-external-main-local-python-package-play1
    #if: startsWith(github.ref, 'refs/heads/bu-')
    #if: ${{ true }}
    # TODO Please comment
    if: false
    strategy:
      # So we'll try to execute jobs in all environments, even if one of them fails
      fail-fast: false
      matrix:
        # TODO We didn't manage to have multiple environments
        target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    # TODO Is it mandatory?
    permissions:
      id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write  # For publish_python_package.yml@main
      pull-requests: write # For publish_python_package.yml@main
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      component-name: google-contact-local-python
      repo-name: google-contact-local-python-package
      #branch_name: ${{ github.ref }}
      # Needed for run_python_package
      #branch-name: BU-2206--develop-google-contact-local-python-package--Tal-Goodman-
      branch-name: dev
      #repo_directory_name: profile_reddit_restapi_imp_local_python_package
      # TODO Misson _local_
      #repo_directory_name: profile_reddit_restapi_imp_python_package
      repo-directory: google-contact-local-python-package
      #package-directory: group_remote
      #sql2code_command_line: python .\.venv\Lib\site-packages\sql_to_code\sqltocode.py --schema database --table table_definition_table --output_path .\database_mysql_local\src\
      # At this point, the GitHub Submodule brings only the dev branch, so we can use it only after we merge Sql2Code to the dev
      #sql2code_command_line: "python sql2code/sqltocode_local_python/sql_to_code/src/sqltocode.py --schema database --table table_definition_table --output_path ./database_mysql_local/src"
      #is-run-local: true
      #is-rds-security-group: false
      #is-publish-to-test-pypi: false # Not working
      # is-publish-to-non-test-pypi is not defined in the referenced workflow.
      #is-publish-to-non-test-pypi: true
      #logger-minimum-severity: 1
      #TODO Should comment or change to false if we didn't authenticate the user interactively manually before running the job
      is-google-user-authenticated-manually: false
      # TODO Should be based on Environment Name, used by run_python_package.yml, which calls run_python_tests.yml
      google-user: <EMAIL>


  # Shall we run it instead of the previous build step (it is also part of the run_python_package, we tried to take it out)
  run-google-contact-local-python-tests:
    name: GoogleContLocalP(dev) Run Python Tests (w/o build) trying to use google-user
    needs: publish-user-external-main-local-python-package-play1
    if: startsWith(github.ref, 'refs/heads/bu-')
    #if: ${{ true }}
    # TODO Please comment
    #if: false
    strategy:
      # So we'll try to execute jobs in all environments, even if one of them fails
      fail-fast: false
      matrix:
        # TODO We didn't manage to have multiple environments
        target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    # No need to run the tests twice
    #if: github.ref=='refs/heads/dev'
    # Disable until we fix it
    #if: false
    # Let's try to run it in parallel to the main job
    #needs: run_remote_python
    #strategy:
      #matrix:
        # TODO We didn't manage to have multiple environments
        #target-environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_tests.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      #component-name: 
      repo-name: google-contact-local-python-package
      repo-directory: google-contact-local-python-package
      branch-name: dev
      #TODO We should bring it back to dev ASAP
      #branch-name: dev
      #package_directory_name: google-contact_local
      #is_rds_security_group: false
      #is_publish_to_non_test_pypi: true
      # TODO Make it based on the Environment Name
      google-user: <EMAIL>
      #logger-minimum-severity: ${{ inputs.logger-minimum-severity }}

      
  run-real-estate-realtor-com-selenium-imp-local-python-package-play1-dev:
    name: RealestateRealtorComSelImpLocalP(dev)Play1 Run
    needs: publish-user-external-main-local-python-package-play1
    if: startsWith(github.ref, 'refs/heads/bu-')
    strategy:
      fail-fast: false
      matrix:
        target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_tests.yml@main
    secrets: inherit
    with:
      brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      repo-name: real-estate-realtor-com-selenium-imp-local-python-package
      repo-directory: real-estate-realtor-com-selenium-imp-local-python-package
