import {
  loggerRemote,
  ComponentCategory,
  ComputerLanguage,
} from "@circles-zone/logger-remote";
import { RemoteLoggerService } from "@circles-zone/logger-remote/dist/remote.logger.service.js";
// TODO Move environmentName to environment or typescript-sdk-remote repos
import { login } from "@circles-zone/authentication-remote";
import fetch from "node-fetch";
import {languageCode} from "@circles-zone/language-remote";
import { SystemContext , populateSystemContext} from "@circles-zone/typescript-sdk-remote"
// TODO Why?
// import { environmentName } from "@circles-zone/authentication-remote/dist/authenticate-remote.js";
import { EnvironmentName } from "@circles-zone/url-remote";
import { isValidUserJwtV2 } from "@circles-zone/authentication-remote/dist/authenticate-remote.js";
export const USER_CONTEXT_REMOTE_TYPESCRIPTS_COMPONENT_ID = 189;
export const USER_CONTEXT_REMOTE_TYPESCRIPTS_COMPONENT_NAME =
  "user-context-remote-typescript";
const logger_init_obj = {
  componentId: USER_CONTEXT_REMOTE_TYPESCRIPTS_COMPONENT_ID,
  componentName: USER_CONTEXT_REMOTE_TYPESCRIPTS_COMPONENT_NAME,
  componentCategory: ComponentCategory.Code,
  computerLanguage: ComputerLanguage.Typescript,
  developerEmailAddress: "<EMAIL>",
  entityTypeId: -557642

};
export interface Tenant{ //TODO: move it to another file
  brandName: string;
  environmentName: string;
}
export interface UserIdentifierAndPassword{
  userIdentifier: string,
  password: string 
}
export interface Credentials{
  credentialsData: UserIdentifierAndPassword | string
}

// TODO Rename all Types to start with T i.e. TUserRole
export type userRole = "ADMIN" | "USER"

// TODO Shall we change it to UserContextRemote
export class UserContext {
  private logger: RemoteLoggerService;
  private realUserId: number | null = null;
  private effectiveUserId: number | null = null;
  private realProfileId: number | null = null;
  private effectiveProfileId: number | null = null;
  private userJwt: string | null = null;
  private userFirstName: string | null = null;
  private userLastName: string | null = null;
  private userRole: [userRole] | null = null;
  private userStars: number | null = null;
  private userMainEmail: string | null = null;
  private profileStars: number | null = null;
  private brandId: number | null = null;
  private subscriptionId: number | null = null;
  private subscriptionName: string | null = null;
  private systemContext?: SystemContext; // contains the brand name, environment name, user identifier and password
  private tenant?: Tenant; // contains the brand name and environment name
  private static userContext: UserContext | null;
  private constructor(
    systemContext: {
      brandName?: string, 
      environmentName?: string, 
      userIdentifier?: string, 
      password?: string 
    }
  ) {
    this.systemContext = populateSystemContext(
    {
      brandName: systemContext.brandName,
      environmentName: systemContext.environmentName,
      userIdentifier: systemContext.userIdentifier,
      password: systemContext.password,
    })

    // TODO LOGGER_MINIMUM_SEVERITY Environment Variable otherwise const DEFAULT_LOGGER_MINIMUM_SEVERITY
    this.logger = loggerRemote(this.systemContext.brandName as string, this.systemContext.environmentName as string);// insertLoginEnvironmentVariables checks for undefined
    this.logger.init("user-context-remote-typescript-package", logger_init_obj);
  }

  // TODO Do we need it?
  static async init(
    tenant: Tenant,
    credentials?: Credentials
  ): Promise<UserContext>;
  static async init(
    brandName: string,
    environmentName: string,
    credentials?: UserIdentifierAndPassword | string
  ): Promise<UserContext>;
  // TODO Do we need it?
  static async init(
    brandNameOrTenant: string | Tenant,
    environmentNameOrCredentials?: string | Credentials,
    credentials?: UserIdentifierAndPassword | string
  ): Promise<UserContext> {
    let brandName: string;
    let environmentName: string;
    // TODO delete all finalCredentials
    // let finalCredentials: UserIdentifierAndPassword | string | undefined;

    if (typeof brandNameOrTenant === 'string') {
      brandName = brandNameOrTenant;
      environmentName = environmentNameOrCredentials as string;
      // finalCredentials = credentials;
    } else {
      brandName = brandNameOrTenant.brandName;
      environmentName = brandNameOrTenant.environmentName;
      // const creds = environmentNameOrCredentials as Credentials;
      // finalCredentials = typeof creds?.credentialsData === 'string' ? creds.credentialsData : creds?.credentialsData;
    }
    // TODO Check what happens when Jwt_SECRET_KEY missing and write to the logger.error("Please make sure you have Jwt_SECRET_KEY environment variable")
    let isLoginSuccessful: boolean;

    try {
      if (typeof credentials === "string") {
        this.userContext = new UserContext({brandName: brandName, environmentName: environmentName}); 
        const userJwt = credentials;
        isLoginSuccessful = await this.userContext.loginWithUserJwt(
          userJwt,
          this.userContext.systemContext?.brandName as string,
          this.userContext.systemContext?.environmentName as string
        );
        if (!isLoginSuccessful)
          throw new Error("Failed to login when credentials is a string (please make sure the user has subscription)");
      } else if(credentials && credentials.userIdentifier && credentials.password){
          this.userContext = new UserContext({
            brandName: brandName,
            environmentName: environmentName,
            userIdentifier: credentials.userIdentifier,
            password: credentials.password}); 
          this.userContext.logger.start("UserContext initialized", {
            userContext: this.userContext,
            tenant: {brandName: brandName, environmentName: environmentName},
            credentials: credentials,
          });
           // TODO Can we use private static while loginWithUserIdentifierAndPassword() update it? 
           // TODO when login is successful call a function that inserts to login_history_table
          isLoginSuccessful = await this.userContext.loginWithUserIdentifierAndPassword(
          {  userIdentifier: this.userContext.systemContext?.userIdentifier as string,
            password: this.userContext.systemContext?.password as string
          },
          {
            brandName: this.userContext.systemContext?.brandName as string,
            environmentName: this.userContext.systemContext?.environmentName as string  
          }
          
          );     
        }
        else{ // init without parameters
          this.userContext = new UserContext({brandName: brandName, environmentName: environmentName}); 
          this.userContext.logger.start("UserContext initialized", {
            userContext: this.userContext,
            tenant: {brandName: brandName, environmentName: environmentName},
            credentials: credentials,
          });
          isLoginSuccessful = await this.userContext.loginWithUserIdentifierAndPassword(
            { 
              userIdentifier: this.userContext.systemContext?.userIdentifier as string,
              password: this.userContext.systemContext?.password as string 
            },
            {
              brandName: this.userContext.systemContext?.brandName as string,
              environmentName: this.userContext.systemContext?.environmentName as string
            }
          );        
          if (!isLoginSuccessful) {  
            const message = `Failed to login when credentials is empty (please make sure the user has subscription)`;  
            this.userContext.logger.error(message)  
            throw new Error(message);  
          } 
        }
      }
    catch (error) {
      if(this.userContext)
      {
        this.userContext.logger.error(
          "Failed to initialize UserContext",
          error as Error
        );
        this.userContext.logger.end("UserContext initialized", {
          usercontext: this.userContext, 
        });
      }
      throw error;
    }
    this.userContext.logger.end("UserContext initialized", {
      userContext: this.userContext,
    });
    return this.userContext;
  } // init


  static getInstance(): UserContext {
    if (!this.userContext) {
      throw new Error(
        "UserContext is not initialized, call UserContext.init() first"
      );
    }
    this.userContext.logger.info("UserContext.getInstance()", {
      userContext: this.userContext,
    });
    return this.userContext;
  }

  // TODO: use authenticate-remote log-out function to reset the instance
  static reset() {
    this.userContext?.logger?.info("UserContext.reset()", {
      userContext: this.userContext,
    });
    // TODO Shall we use delete operator if there is value?  
    this.userContext = null;
  }

  // Perform login with authentication-remote login() function using credentials
  private async loginWithUserIdentifierAndPassword(
    // TODO keep the same order everywhere brand, environment, login
    credentials: { userIdentifier: string; password: string },
    tenant :{brandName: string, environmentName: string}
  ): Promise<boolean> {
    this.logger.start("UserContextService authenticate()", {
      credentials: credentials,
      tenant: tenant
    });
    try {
      if (!credentials)
        throw new Error(
          "Either define PRODUCT_USER_IDENTIFIER and PRODUCT_PASSWORD environment variables or pass credentials to init()"
        );
      if (!credentials.userIdentifier)
        throw new Error(
          "Either define PRODUCT_USER_IDENTIFIER environment variable or pass userIdentifier (email or username) to init()"
        );
      if (!credentials.password)
        throw new Error(
          "Either define PRODUCT_PASSWORD environment variable or pass password to init()"
        );
      const nodeFetch: (
        input: RequestInfo | URL,
        init?: RequestInit | undefined
        //  eslint-disable-next-line @typescript-eslint/no-explicit-any
      ) => Promise<Response> = fetch as any;
      const loginResponse = await login(
        nodeFetch,
        credentials,
        tenant.brandName,
        tenant.environmentName as EnvironmentName,
        // TODO Shall we use object with all four parameters if we have such?  
      );
      // TODO Can we delete credentials.password; 
      if (!loginResponse) {
        throw new Error(`Login failed userIdentifier: ${credentials.userIdentifier}`);
      }
      this.logger.info("UserContextService authenticate()", {
        loginResponse: loginResponse,
      });
      // Access USER_JWT_DATA_MEMBER from loginResponse
      const userJwt: string = loginResponse?.data?.userJwt;
      if (!userJwt) {
        throw new Error(
          `Failed to get userJwt for username: ${credentials.userIdentifier}`
        );
      }
      //TODO execute the setters for the profileId and userId
      this.userFirstName = loginResponse?.data?.userDetails?.firstName;
      this.userLastName = loginResponse?.data?.userDetails?.lastName;
      this.userJwt = userJwt;
      this.realProfileId = loginResponse?.data?.userDetails?.profileId;
      this.effectiveProfileId = loginResponse?.data?.userDetails?.profileId;
      this.realUserId = loginResponse?.data?.userDetails?.userId;
      this.effectiveUserId = loginResponse?.data?.userDetails?.userId;
      this.profileStars = loginResponse?.data?.userDetails?.profileStars;
      this.userStars = loginResponse?.data?.userDetails?.userStars;
      this.subscriptionName = loginResponse?.data?.userDetails?.subscriptionName;
      this.subscriptionId = loginResponse?.data?.userDetails?.subscriptionId;
      this.brandId = loginResponse?.data?.userDetails?.brandId;
      this.userMainEmail = loginResponse?.data?.userDetails?.userMainEmail;
      // TODO Lvalue Rvalue, we prefer .userRole;
      this.userRole=loginResponse?.data?.userDetails?.role;
      // TODO are you sure we need to call to this function?
      //await this.processUserJwt(this.userJwt as string, brandName, environmentName);
      this.logger.end("UserContextService authenticate()", {
        userJwt: userJwt,
        // TODO What is result?
        result: true,
      });
      return true;
    } catch (error) {
      // TODO Not to write the password in the log as clear text
      this.logger.exception(
        // TODO add the error to the 1st parameter
        `Failed to authenticate credentials=${credentials.userIdentifier} ${credentials.password}`,
        error as Error
      );
      // TODO message = .....
      this.logger.end("UserContextService authenticate()", { result: false });
      throw error;
    }
  }

  // Perform login with userJwt
  private async loginWithUserJwt(
    userJwt: string,
    brandName: string,
    environmentName: string
  ): Promise<boolean> {
    this.logger.start("UserContextService loginWithUserJwt()", {
      userJwt: userJwt,
      brandName: brandName,
      environmentName: environmentName,
    });
    try {
      await this.processUserJwt(userJwt,brandName, environmentName);
      this.logger.end("UserContextService loginWithUserJwt()", {
        userJwt: userJwt,
      });
      return true;
    } catch (error) {
      this.logger.exception(
        `Failed to loginWithUserJwt() userJwt=${userJwt}`,
        error as Error
      );
      this.logger.end("UserContextService loginWithUserJwt()");
      return false;
    }
  }

  // TODO setRealProfileId()
  setEffectiveProfileId(profileId: number): void {
    // TODO Replace string with constant from role-local-typescript-package
    this.logger.start("UserContextService setEffectiveProfileId()", {
      profileId: profileId,
    })
    if(this.userRole?.includes('ADMIN')) 
    {
      this.effectiveProfileId = profileId;
      this.logger.info(`UserContextService setEffectiveProfileId()`, {
        effectiveProfileId: this.effectiveProfileId,
      });
    }
    else{
      this.logger.info(`UserContextService setRealProfileIdForAdmin() wasn't because changed not admin`, {
        realProfileId: this.realProfileId,
      });
    }
    this.logger.end("UserContextService setEffectiveProfileId() ended")
  }
  getRealUserId(): number | null {
    this.logger.start("UserContextService getRealUserId() started")
    this.logger.info(`UserContextService getRealUserId()`, {
      realUserId: this.realUserId,
    });
    this.logger.end("UserContextService getRealUserId() ended")
    return this.realUserId;
  }
  getRealProfileId(): number | null {
    this.logger.start("UserContextService getRealProfileId() started")
    this.logger.info(`UserContextService getPgetRealProfileIdrofileId()`, {
      realProfileId: this.realProfileId,
    });
    this.logger.end("UserContextService getRealProfileId() ended")
    return this.realProfileId;
  }
  getEffectiveUserId(): number | null {
    this.logger.start("UserContextService getEffectiveUserId() started")
    this.logger.info(`UserContextService getEffectiveUserId()`, {
      effectiveUserId: this.effectiveUserId,
    });
    this.logger.end("UserContextService getEffectiveUserId() ended")
    return this.effectiveUserId;
  }
  getEffectiveProfileId(): number | null {
    this.logger.start("UserContextService getEffectiveProfileId() started")
    this.logger.info(`UserContextService getEffectiveProfileId()`, {
      effectiveProfileId: this.effectiveProfileId,
    });
    this.logger.end("UserContextService getEffectiveProfileId() ended")
    return this.effectiveProfileId;
  }

  getUserRole(): [string] | null {
    this.logger.start("UserContextService getUserRole() started")
    this.logger.info(`UserContextService getUserRole()`, {
      userRole: this.userRole,
    });
    this.logger.end("UserContextService getUserRole() ended")
    return this.userRole;
  }

  // TODO add UserRole, remove UserRole
  private setUserRole(UserRole: [userRole]): void {
    this.logger.start("UserContextService setUserRole() started")
    this.logger.info(`UserContextService setUserRole()`, {
      UserRole: UserRole,
    });
    this.userRole = UserRole;
    this.logger.end("UserContextService setUserRole() ended")
  }

  getUserStars(): number | null {
    this.logger.start("UserContextService getUserStars() started")
    this.logger.info(`UserContextService getUserStars()`, {
      userStars: this.userStars,
    });
    this.logger.end("UserContextService getUserStars() ended")
    return this.userStars;
  }

  getBrandId(): number | null {
    this.logger.start("UserContextService getBrandId() started")
    this.logger.info(`UserContextService getBrandId()`, {
      brandId: this.brandId,
    });
    this.logger.end("UserContextService getBrandId() ended")
    return this.brandId;
  }

  getSubscriptionId(): number | null {
    this.logger.start("UserContextService subscriptionId() started")
    this.logger.info(`UserContextService getSubscriptionId()`, {
      subscriptionId: this.subscriptionId,
    });
    this.logger.end("UserContextService subscriptionId() ended")
    return this.subscriptionId;
  }

  getSubscriptionName(): string | null {
    this.logger.start("UserContextService getSubscriptionName() started")
    this.logger.info(`UserContextService getSubscriptionName()`, {
      subscriptionName: this.subscriptionName,
    });
    this.logger.end("UserContextService getSubscriptionName() ended")
    return this.subscriptionName;
  }

  getUserJwt(): string | null {
    this.logger.start("UserContextService getUserJwt() started")
    this.logger.info(`UserContextService getUserJwt()`, {
      userJwt: this.userJwt,
    });
    this.logger.end("UserContextService getUserJwt() ended")
    return this.userJwt;
  }

  getUserFirstName(): string | null {
    this.logger.start("UserContextService getUserFirstName() started")
    this.logger.info(`UserContextService getUserFirstName()`, {
      userFirstName: this.userFirstName,
    });
    this.logger.end("UserContextService getUserFirstName() ended")
    return this.userFirstName;
  }

  getUserLastName(): string | null {
    this.logger.start("UserContextService getUserLastName() started")
    this.logger.info(`UserContextService getUserLastName()`, {
      userLastNane: this.userLastName,
    });
    this.logger.end("UserContextService getUserLastName() ended")
    return this.userLastName;
  }

  private setUserFirstName(userFirstName: string): void {
    this.logger.info(`UserContextService setUserFirstName()`, {
      userFirstName: userFirstName,
    });
    this.userFirstName = userFirstName;
  }

  private setUserLastName(userLastName: string): void {
    this.logger.info(`UserContextService setUserLastName()`, {
      userLastName: userLastName,
    });
    this.userLastName = userLastName;
  }
  
  getProfileStars(): number | null {
    this.logger.start("UserContextService getProfileStars() started")
    this.logger.info(`UserContextService getProfileStars()`, {
      profileStars: this.profileStars,
    });
    this.logger.end("UserContextService getProfileStars() ended")
    return this.profileStars;
  }

  getUserMainEmail(): string | null {
    this.logger.start("UserContextService getUserMainEmail() started")
    this.logger.info(`UserContextService getUserMainEmail()`, {
      userMainEmail: this.userMainEmail,
    });
    this.logger.end("UserContextService getUserMainEmail() ended")
    return this.userMainEmail;
  }

  getProfilePreferredLangCode(): string {
    this.logger.start("UserContextService getProfilePreferredLangCode() started")
    this.logger.info(`UserContextService getUserLanguage() (en for now)`);
    this.logger.end("UserContextService getProfilePreferredLangCode() ended")
    // TOOD Replace the hardcoded value
    return languageCode.EN;
  }

  private setProfilePreferredLangCode(newUserLanguage: string): void {
    this.logger.info(`UserContextService setUserLanguage() activated langCode=${newUserLanguage}` );  
    // TODO Please do the real thing
    this.logger.warn(`TODO UserContextService setUserLanguage() is not working yet, should update the database `);
  } 
  private async setUserJwt(userJwt: string): Promise<void> {
    this.logger.start("UserContextService setUserJwt() started")
    this.logger.info(`UserContextService setUserJwt()`, { userJwt: userJwt });
    this.userJwt = userJwt;
    this.logger.end("UserContextService setUserJwt() ended")
  }

  private setRealUserId(realUserId: number): void {
    this.logger.start("UserContextService setRealUserId() started")
    this.logger.info(`UserContextService setRealUserId()`, {
      realUserId: realUserId,
    });
    this.realUserId = realUserId;
    this.logger.end("UserContextService setRealUserId() ended")
  }

  private setRealProfileId(realProfileId: number): void {
    this.logger.start(`UserContextService setRealProfileId() started`)
    this.logger.info(`realProfileId: ${realProfileId}`, {
      realProfileId: realProfileId,
    });
    this.realProfileId = realProfileId;
    this.logger.end(`UserContextService setRealProfileId() ended`)
  }

  // TODO Add tests to setEffectiveUserId if user in Role Admin and case user is not in Role Admin
  private setEffectiveUserId(effectiveUserId: number): void {
    this.logger.start(`UserContextService setEffectiveUserId() started effectiveUserId=${effectiveUserId}`, {
      effectiveUserId: effectiveUserId,
    });
    // TODO Check if Roles contains Admin (like in setEffectuveProfileId() otherwise exception
    if(this.userRole?.includes('ADMIN')) {
      this.effectiveUserId = effectiveUserId;
      this.logger.info(`setEffectiveUserId(effectiveUserId=${effectiveUserId}) succeded`);
    }
    else {
      this.logger.warn(`setEffectiveUserId(effectiveUserId=${effectiveUserId}) failed`);
    }
    this.logger.end(`UserContextService setEffectiveUserId() ended effectiveUserId=${effectiveUserId}`)
  }

  private setProfileStars(profileStars: number): void {
    this.logger.info(`UserContextService setProfileStars()`, {
      profileStars: profileStars,
    });
    this.profileStars = profileStars;
  }
  private setSubscriptionName(subscriptionName: string): void {
    this.logger.info(`UserContextService setSubscriptionName()`, {
      subscriptionName: subscriptionName,
    });
    this.subscriptionName = subscriptionName;
  }
  private setSubscriptionId(subscriptionId: number): void {
    this.logger.info(`UserContextService setSubscriptionId()`, {
      subscriptionId: subscriptionId,
    });
    this.subscriptionId = subscriptionId;
  }
  private setBrandId(brandId: number): void {
    this.logger.info(`UserContextService setBrandId()`, {
      brandId: brandId,
    });
    this.brandId = brandId;
  }
  private setMainEmail(userMainEmail: string): void {
    this.logger.info(`UserContextService setMainEmail()`, {
      userMainEmail: userMainEmail,
    });
    this.userMainEmail = userMainEmail;
  }
  private setUserStars(userStars: number): void { // is user to populate the user's stars after logging in 
    this.logger.info(`UserContextService setUserStars()`, {
      userStars: userStars,
    });
    this.userStars = userStars;
  }

  private processUserJwt = async (
    userJwt: string,
    brandName: string,
    environmentName: string
  ): Promise<void> => {
    this.logger.start(`UserContextService extractIdFromJwt()`, {
      userJwt: userJwt,
      brandName: brandName,
      environmentName: environmentName
    });
    try {
      
      const nodeFetch: (
        input: RequestInfo | URL,
        init?: RequestInit | undefined
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ) => Promise<Response> = fetch as any;
      const userJwtPayload = await isValidUserJwtV2(
        nodeFetch,
        userJwt,
        brandName,
        environmentName as EnvironmentName
      );
      
      // TODO add logger.error()
      if (!userJwtPayload) throw new Error(`user-context-remote-typescript-package jwt decode failed`);
      this.logger.info(`UserContextService extractIdFromJwt()`, {
        userJwtPayload: userJwtPayload,
      });
      this.logger.info(`UserContextService extractIdFromJwt()`, {
        payload_data_userDetails: userJwtPayload.data.userDetails,
      });

      this.setUserRole(userJwtPayload.data.userDetails.roles[0])
      this.setUserJwt(userJwt);
      this.setRealProfileId(userJwtPayload.data.userDetails.profileId);
      this.setRealUserId(userJwtPayload.data.userDetails.userId);
      this.setEffectiveProfileId(userJwtPayload.data.userDetails.profileId);
      this.setEffectiveUserId(userJwtPayload.data.userDetails.userId);

      this.logger.end(`UserContextService extractIdFromJwt()`);
    } catch (error) {
      this.logger.exception(
        `failed to decode userJwt=${userJwt}`,
        error as Error
      );
      this.logger.end(`UserContextService extractIdFromJwt()`);
      throw new Error(`failed to decode userJwt=${userJwt}`);
    }
  };
}
