{"name": "@circles-zone/user-context-remote", "version_comment": "https://github.com/circles-zone/user-context-remote-typescript-package/pkgs/npm/user-context-remote", "version": "0.0.94-1235", "description": "", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["/dist", "/src"], "scripts": {"huskyInstall": "husky install", "prepare_comment": "The run all and npm-s workaround is so we can run the scripts without using && which only works on Linux", "prepare": "cd .. && husky user-context-remote-typescript-package/.husky", "upgrade_packages": "npm update --save", "tsc": "tsc", "compile": "npx tsc", "prebuild_comment": "TODO Should it be `prebuild` or `preinstall` https://stackoverflow.com/questions/41123631/how-to-get-the-version-from-the-package-json-in-typescript", "prebuild": "node -p \"'export const PACKAGE_VERSION = ' + JSON.stringify(require('./package.json').version) + ';'\" > user-context-remote/src/version.ts", "build_comment": "We recommend doing build and not only compile", "build": "npm i && npx tsc", "lint_comment": "TODO We must have .eslintrc.cjs file", "lint_old": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint": "eslint **/src/**/*.ts **/tests/**/*.ts --report-unused-disable-directives --max-warnings 0", "test_comment": "TODO Upgrade jest to the latest version and convert jest.config.cjs to .ts", "test": "NODE_OPTIONS=\"--experimental-vm-modules --no-warnings\" jest --config ./jest.config.cjs --forceExit --detectOpenHandles --coverage", "dev_comment": "TODO Please rename MarketplaceGoodsBackend to your directory", "dev": "nodemon --watch 'MarketplaceGoodsBackend/Src/*.ts --exec ts-node src/index.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@circles-zone/authentication-remote": ">=0.0.85-2593", "@circles-zone/language-remote": ">=0.0.7", "@circles-zone/logger-remote": ">=0.0.164-2639", "@circles-zone/typescript-sdk-remote": ">=0.0.36", "jwt-decode": "3.1.2", "node-fetch": "^2.7.0", "npm": "^10.2.5", "ts-node": "^10.9.1", "uuid": "^9.0.0"}, "devDependencies": {"@babel/preset-typescript": "^7.27.1", "@types/jest": "^29.5.14", "@types/node-fetch": "^2.6.4", "@types/uuid": "^9.0.3", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "@typescript-eslint/typescript-estree": "^6.12.0", "eslint": "^9.9.0", "husky": "^9.1.4", "jest": "^29.7.0", "npm-run-all": "^4.1.5", "ts-jest": "^29.4.0", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1"}}