# This workflow will run tests using node and then publish a package to GitHub Packages when a release is created
# For more information see: https://docs.github.com/en/actions/publishing-packages/publishing-nodejs-packages

# TODO: Please change the <project-name> to the folder/project name everywhere and delete this line
name: Build & Publish <project-name> Local|Remote TypeScript Node.js Package to Private GitHub npm play1

on:
  # TODO: Please make sure your code complies with our Definition of Done (DoD), `npm prepare`, `npm test`, tests cover 95% of the code, resolve all lint warnings, GitHub Action is Green in play1 before you create a Pull Request to the dev branch (dvlp1 Environment) 
  #push:
    #branches: [ "BU-*" ]
  pull_request: 
    branches: [ dev ]
    
env:
  brand_name: Circlez
  environment_name: play1
  ## TODO: <repo_name> should be the folder/directory of the project. Same as the repo name but with underscore. Root folder should have only .github directory and the project directory.
  #repo_name: <project-name>
  # TODO: repo_name same as repo name, but with underscore instead of hypean/minus/dash as the main directory/folder
  # TODO: Make sure the /src and /tests directories are under the directory with the same name as the repo called the project directory i.e. /<project-name>/src We should create/update the directory name to be the same as the repo name i.e. /time_local_python_package (with underscore). In the root directory, we should have only .gitignore
  repo_name: relationship-type-typeorm-local-typescript-package # TODO i.e. unified_json_api_python_package
  #package name is used in Python at this point in time it is not in use in TypeScript
  #package_name: <package-directory-with-underlines> # TODO i.e. unified_json_api or unified_json_api_local 

jobs:
  build:
    name: RelationshipTypeTypeOrmLocalTS Old
    runs-on: ubuntu-latest
    environment:
      name: play1
      url: http://${{ env.environment_name }}.circ.zone
    steps:
      - uses: actions/checkout@v4.0.0

      - name: Setup Python and Node
        uses: actions/setup-node@v3.6.0
        with:
          node-version: 20 # 16 -> 20
          # Is it mandatory?
          #registry-url: 'https://npm.pkg.github.com'

      # As we want the team to be able to test it locally without GHA, we included the read:package token in .npmrc file so we don't need this phase
      - name: Authenticate PUT with private GitHub package to avoid 401 (npm ERR! code E401, npm ERR! 401 Unauthorized)
        # Can we delete secrets GIT_HUB_PACKAGE_WRITE_TOKEN?
        ##run: echo "//npm.pkg.github.com/:_authToken=${{ secrets.GIT_HUB_PACKAGE_WRITE_TOKEN }}" > ~/.npmrc
        run: |
          cd ./${{ env.repo_name }}
          echo "@circles-zone:registry=https://npm.pkg.github.com" > .npmrc
          echo "//npm.pkg.github.com/:_authToken=${{ secrets.GITHUB_TOKEN }}" >> .npmrc
          
      # If we get 403 Permission permission_denied: read_package, we need to add read permission in the repo we try to access in https://github.com/orgs/circles-zone/packages/npm/database-without-orm-local-typescript-package/settings for example
      # Should we do npm install before npm ci?
      # 'npm ci' executing also 'npm tsc' which is equivalent to 'npm run prepare'
      # No need to create .npmrc before npm ci
      # In some cases it is needed to run both "npm i" and "npm ci" (i.e. marketplace-goods-graphql-typescript-serverless-com)
      # In the development environment it is better to run `npm i` and not `npm ci`
      - name: npm i
        run: |
          cd ./${{ env.repo_name }}
          #npm ci --ignore-scripts
          npm i

      # Some of the errors will be shown only if doing npx tsc (in case `npm i` is not executing npx tsc)
      - name: npx tsc
        run: |
          cd ./$repo_name
          npx tsc
        #env:
        # Reached heap limit Allocation failed - JavaScript heap out of memory
        # https://www.makeuseof.com/javascript-heap-out-of-memory-error-fix/
        # NODE_OPTIONS: --max-old-space-size=4096

      - name: npm run lint
        run: |
          cd ./$repo_name
          npm install eslint@latest --save-dev
          npm run lint

      # If this repo accessing the database
      - name: Get GitHub Action (GHA) runner IP
        id: ip
        uses: haythem/public-ip@v1.3
        with:
          maxRetries: 40

      - name: Setting AWS_DEFAULT_REGION and AWS_SG_NAME environment variables..
        run: |
          echo "AWS_DEFAULT_REGION=us-east-1" >> $GITHUB_ENV
          # RDS/MySQL EC2 Security Group in Management/Master AWS Account 
          echo "AWS_SG_NAME=mysql_mang_sg" >> $GITHUB_ENV

      - name: Add GitHub Actions (GHA) runner IP to EC2 Security Group in Master/Management AWS Account
        run: |
          aws ec2 authorize-security-group-ingress --group-name ${{ env.AWS_SG_NAME }} --protocol tcp --port 3306 --cidr ${{ steps.ip.outputs.ipv4 }}/32
        env:
          # Since RDS/MySQL is currently in Management/Master AWS Account
          AWS_ACCESS_KEY_ID: ${{ secrets[format('AWS_ACCESS_KEY_ID_{0}', 'MANG1')] }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', 'MANG1')] }}
          AWS_DEFAULT_REGION: ${{ env.AWS_DEFAULT_REGION }}

      # Should we use "npm test --runInBand"?
      - name: npm test
        run: |
          cd ./$repo_name
          # https://stackoverflow.com/questions/********/npm-test-coverage-never-exits
          # I added '-- coverage'
          npm test --detectOpenHandles --coverage
        env:
          # TODO: Please comment and then delete all variables not being used.
          # TODO: In case remote-typescript-package we need only ENVIRONMENT:
          # TODO Check if we want to copy from storage-local
          BRAND_NAME: ${{ env.brand_name }}
          ENVIRONMENT_NAME: ${{ env.environment_name }}
          # We changed PRODUCT_USERNAME to PRODUCT_USER_IDENTIFIER
          # TODO Please make sure your code works without PRODUCT_USERNAME, so we can delete PRODUCT_USERNAME
          PRODUCT_USERNAME: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', env.environment_name)] }}
          PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', env.environment_name)] }}
          PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', env.environment_name)] }}
          # Per Environment
          LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', env.environment_name)] }}
          # TODO: RDS environment variables are needed for Local which accesses the RDS (MySQL) and should be deleted in case of Remote
          RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', env.environment_name)] }}
          RDS_USERNAME: ${{ vars.RDS_USERNAME }}
          RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}
          DEBUG: 1 # OLD, should be replaced by "LOGGER_MINIMUM_SEVERITY: Warning" and .logger.json
          # https://github.com/circles-zone/logger-local-python-package/blob/dev/logger_local_python_package/logger_local/src/MessageSeverity.py
          #LOGGER_MINIMUM_SEVERITY: 1
          LOGGER_MINIMUM_SEVERITY: Warning

      - name: Remove Github Actions IP from a security group
        run: |
          aws ec2 revoke-security-group-ingress --group-name ${{ env.AWS_SG_NAME }} --protocol tcp --port 3306 --cidr ${{ steps.ip.outputs.ipv4 }}/32
        env:
          # Since RDS/MySQL in Master/Management AWS Account
          AWS_ACCESS_KEY_ID: ${{ secrets[format('AWS_ACCESS_KEY_ID_{0}', 'MANG1')] }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', 'MANG1')] }}
          AWS_DEFAULT_REGION: ${{ env.AWS_DEFAULT_REGION }}
        if: always()
  
  # Commenting this job
  # Pro: no duplicate steps
  # Cons: we can't re-run this job
  #publish:
    #needs: build
    #runs-on: ubuntu-latest
    # It seems we don't need this
    # needed for react.js for "npm build"
    #permissions:
      #contents: read
      #packages: write
    #steps:
      #- uses: actions/checkout@v4.0.0

      #- uses: actions/setup-node@v3.6.0
        #with:
          #node-version: 20 # 16 -> 20
          # It seems we don't need those
          ##registry-url: https://npm.pkg.github.com/
          ## scope: "@circles-zone"
      # `npm i` works better for us than npm ci (i.e. local-storage-typescript backend) 
      # if we get 403 Permission permission_denied: read_package, we need to add read permission in the repo we try to access in https://github.com/orgs/circles-zone/packages/npm/database-without-orm-local-typescript-package/settings for example    
      #- name: npm i
        #run: |
          #cd ./$repo_name
          #npm i
              
      - name: Authenticate PUT with private GitHub package to avoid 401
        run: |
          # To solve "code E403 npm ERR! 403 403 Forbidden - PUT https://npm.pkg.github.com/circles-zone/@circles-zone%2fdatabase-typeorm-local - Permission permission_denied: The token provided does not match expected scopes.
          echo "@circles-zone:registry=https://npm.pkg.github.com" > ./$repo_name/.npmrc
          echo "//npm.pkg.github.com/:_authToken=${{ secrets.GITHUB_TOKEN }}" >> ./$repo_name/.npmrc
          
      # Increase the version number instead of updating package.json manually (other solution might be 'grunt bump:patch')
      #- run: git config --global user.email "<EMAIL>"
      #- run: git config --global user.name "Circles"

      # npm version patch is not working yet
      #- name: npm version patch
        #run: |
          # npm version patch should run in the project directory and not in the root directory
          #cd ./$repo_name
          #npm version patch

      # Should make sure the package.json version is unique
      # Should add @circles-zone/ to the package "name" in package.json
      # If there is no "name" in package.json you will get ENEEDAUTH error
      # If we have "code E403" error we need to generate ./$repo_name/.npmrc in previous step
      - name: npm publish
        run: |
          cd ./${{ env.repo_name }}
          npm publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  relationship-type-typeorm-local-build-publish:
    name: RelationshipTypeTypeOrmLocalTS New
    # Should be deleted
    #needs: build_publish
    strategy:
      matrix:
        # TODO We didn't manage to have multiple environments
        target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/publish_typescript_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      repo-name: relationship-type-typeorm-local-typescript-package
      # We should not state the branch as we want the current branch
      #branch_name: dev
      repo-directory: relationship-type-typeorm-local-typescript-package
      #is_bump_version: false
      #is_rds_security_group: false
